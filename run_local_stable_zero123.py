#!/usr/bin/env python3
"""
使用本地stable_zero123模型运行DreamGaussian
"""

import os
import sys
import torch

def setup_environment():
    """设置环境"""
    # 设置本地模型路径
    os.environ['STABLE_ZERO123_LOCAL'] = 'models/stable_zero123/stable_zero123.ckpt'
    
    # 强制使用CUDA光栅化
    os.environ['FORCE_CUDA_RAST'] = '1'
    
    print("✅ 环境设置完成")

def run_dreamgaussian_local():
    """运行DreamGaussian with本地模型"""
    setup_environment()
    
    # 导入主模块
    from main import GUI
    from omegaconf import OmegaConf
    
    # 加载配置
    config_path = 'configs/image_local.yaml'
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    opt = OmegaConf.load(config_path)
    opt.input = 'data/anya_rgba.png'
    opt.save_path = 'anya_local'
    opt.gui = False
    
    print(f"📋 使用配置: {config_path}")
    print(f"📋 输入图像: {opt.input}")
    print(f"📋 保存路径: {opt.save_path}")
    
    try:
        # 创建GUI实例并运行
        gui = GUI(opt)
        gui.train(opt.iters)
        
        print("🎉 DreamGaussian运行完成！")
        return True
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 DreamGaussian本地模型运行器")
    print("=" * 50)
    
    success = run_dreamgaussian_local()
    sys.exit(0 if success else 1)
