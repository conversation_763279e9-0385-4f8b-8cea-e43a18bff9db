# Simple image config using local stable_zero123
name: "image_simple"
tag: ""
seed: 0

# Input/Output
input: data/anya_rgba.png
save_path: anya_simple
outdir: logs

# Training parameters
iters: 500
batch_size: 1
radius: 2
fovy: 49.1
min_ver: -30
max_ver: 30

# Use stable_zero123
stable_zero123: True
lambda_zero123: 1
lambda_sd: 0

# Mesh parameters
density_thresh: 1
mesh_format: obj

# System parameters
gui: False
force_cuda_rast: True
H: 800
W: 800

# Gaussian parameters
num_pts: 5000
sh_degree: 0
position_lr_init: 0.001
position_lr_final: 0.00002
position_lr_delay_mult: 0.02
position_lr_max_steps: 500
feature_lr: 0.01
opacity_lr: 0.05
scaling_lr: 0.005
rotation_lr: 0.005
percent_dense: 0.01
density_start_iter: 100
density_end_iter: 3000
densification_interval: 100
opacity_reset_interval: 700
densify_grad_threshold: 0.01

# Textured Mesh
geom_lr: 0.0001
texture_lr: 0.2
