#!/usr/bin/env python3
"""
支持断点续传的Zero123模型下载器
专门用于下载DreamGaussian所需的Zero123模型文件
"""

import os
import sys
import time
import requests
import hashlib
from pathlib import Path
from typing import Optional, Dict, List
from urllib.parse import urljoin
import json
from tqdm import tqdm

class ResumableDownloader:
    """支持断点续传的下载器"""
    
    def __init__(self, max_retries: int = 10, chunk_size: int = 8192, timeout: int = 30):
        self.max_retries = max_retries
        self.chunk_size = chunk_size
        self.timeout = timeout
        self.session = requests.Session()
        # 设置User-Agent避免被拒绝
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def download_file(self, url: str, local_path: str, expected_size: Optional[int] = None) -> bool:
        """
        下载单个文件，支持断点续传
        
        Args:
            url: 下载URL
            local_path: 本地保存路径
            expected_size: 期望的文件大小（用于验证）
            
        Returns:
            bool: 下载是否成功
        """
        local_path = Path(local_path)
        local_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 检查是否已经下载完成
        if local_path.exists() and expected_size:
            if local_path.stat().st_size == expected_size:
                print(f"✅ 文件已存在且完整: {local_path}")
                return True
        
        # 获取已下载的大小
        resume_pos = 0
        if local_path.exists():
            resume_pos = local_path.stat().st_size
            print(f"📁 发现部分下载文件，从 {resume_pos:,} 字节处继续...")
        
        for attempt in range(self.max_retries):
            try:
                # 设置Range头进行断点续传
                headers = {}
                if resume_pos > 0:
                    headers['Range'] = f'bytes={resume_pos}-'
                
                print(f"🔄 尝试下载 {attempt + 1}/{self.max_retries}: {url}")
                
                response = self.session.get(url, headers=headers, stream=True, timeout=self.timeout)
                response.raise_for_status()
                
                # 获取文件总大小
                if 'content-range' in response.headers:
                    # 断点续传响应
                    content_range = response.headers['content-range']
                    total_size = int(content_range.split('/')[-1])
                elif 'content-length' in response.headers:
                    # 完整下载响应
                    total_size = int(response.headers['content-length']) + resume_pos
                else:
                    total_size = expected_size or 0
                
                # 打开文件进行写入
                mode = 'ab' if resume_pos > 0 else 'wb'
                with open(local_path, mode) as f:
                    # 创建进度条
                    with tqdm(
                        total=total_size,
                        initial=resume_pos,
                        unit='B',
                        unit_scale=True,
                        desc=local_path.name
                    ) as pbar:
                        
                        downloaded_this_session = 0
                        for chunk in response.iter_content(chunk_size=self.chunk_size):
                            if chunk:
                                f.write(chunk)
                                chunk_size = len(chunk)
                                downloaded_this_session += chunk_size
                                pbar.update(chunk_size)
                
                # 验证下载完成
                final_size = local_path.stat().st_size
                if expected_size and final_size != expected_size:
                    print(f"⚠️  文件大小不匹配: 期望 {expected_size:,}, 实际 {final_size:,}")
                    resume_pos = final_size
                    continue
                
                print(f"✅ 下载完成: {local_path} ({final_size:,} 字节)")
                return True
                
            except (requests.exceptions.RequestException, IOError) as e:
                print(f"❌ 下载失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    wait_time = min(2 ** attempt, 60)  # 指数退避，最大60秒
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
                    # 更新断点位置
                    if local_path.exists():
                        resume_pos = local_path.stat().st_size
                else:
                    print(f"💥 达到最大重试次数，下载失败: {url}")
                    return False
        
        return False

class Zero123ModelDownloader:
    """Zero123模型下载器"""
    
    def __init__(self, cache_dir: Optional[str] = None):
        self.cache_dir = Path(cache_dir) if cache_dir else Path.home() / '.cache' / 'huggingface' / 'hub'
        self.downloader = ResumableDownloader()
        
        # Zero123模型文件列表（从HuggingFace获取）
        self.model_files = {
            'ashawkey/zero123-xl-diffusers': [
                {
                    'filename': 'model_index.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/model_index.json',
                    'size': 562
                },
                {
                    'filename': 'feature_extractor/preprocessor_config.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/feature_extractor/preprocessor_config.json',
                    'size': 546
                },
                {
                    'filename': 'image_encoder/config.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/image_encoder/config.json',
                    'size': 585
                },
                {
                    'filename': 'image_encoder/model.safetensors',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/image_encoder/model.safetensors',
                    'size': 637962808  # ~608MB
                },
                {
                    'filename': 'scheduler/scheduler_config.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/scheduler/scheduler_config.json',
                    'size': 502
                },
                {
                    'filename': 'unet/config.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/unet/config.json',
                    'size': 1781
                },
                {
                    'filename': 'unet/diffusion_pytorch_model.safetensors',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/unet/diffusion_pytorch_model.safetensors',
                    'size': 1719664576  # ~1.6GB
                },
                {
                    'filename': 'vae/config.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/vae/config.json',
                    'size': 601
                },
                {
                    'filename': 'vae/diffusion_pytorch_model.safetensors',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/vae/diffusion_pytorch_model.safetensors',
                    'size': 167335342  # ~160MB
                },
                {
                    'filename': 'zero123.py',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/zero123.py',
                    'size': 30634
                },
                {
                    'filename': 'cc_projection/config.json',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/cc_projection/config.json',
                    'size': 132
                },
                {
                    'filename': 'cc_projection/diffusion_pytorch_model.safetensors',
                    'url': 'https://huggingface.co/ashawkey/zero123-xl-diffusers/resolve/main/cc_projection/diffusion_pytorch_model.safetensors',
                    'size': 1251342  # ~1.2MB
                }
            ]
        }
    
    def download_model(self, model_name: str = 'ashawkey/zero123-xl-diffusers') -> bool:
        """
        下载指定的Zero123模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 下载是否成功
        """
        if model_name not in self.model_files:
            print(f"❌ 未知的模型: {model_name}")
            return False
        
        # 创建模型目录
        model_dir = self.cache_dir / f'models--{model_name.replace("/", "--")}'
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建snapshots目录
        snapshots_dir = model_dir / 'snapshots' / 'main'
        snapshots_dir.mkdir(parents=True, exist_ok=True)
        
        files = self.model_files[model_name]
        total_files = len(files)
        successful_downloads = 0
        
        print(f"🚀 开始下载 {model_name} 模型 ({total_files} 个文件)")
        print(f"📁 保存位置: {snapshots_dir}")
        
        for i, file_info in enumerate(files, 1):
            filename = file_info['filename']
            url = file_info['url']
            expected_size = file_info['size']
            
            local_path = snapshots_dir / filename
            
            print(f"\n📥 [{i}/{total_files}] 下载: {filename}")
            
            if self.downloader.download_file(url, local_path, expected_size):
                successful_downloads += 1
            else:
                print(f"💥 文件下载失败: {filename}")
        
        success_rate = successful_downloads / total_files
        print(f"\n📊 下载完成: {successful_downloads}/{total_files} ({success_rate:.1%})")
        
        if successful_downloads == total_files:
            print("🎉 所有文件下载成功！")
            return True
        else:
            print("⚠️  部分文件下载失败，可以重新运行脚本继续下载")
            return False

def main():
    """主函数"""
    print("🤖 Zero123模型下载器")
    print("=" * 50)
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 检测到虚拟环境")
    else:
        print("⚠️  建议在虚拟环境中运行")
    
    # 创建下载器
    downloader = Zero123ModelDownloader()
    
    # 开始下载
    success = downloader.download_model('ashawkey/zero123-xl-diffusers')
    
    if success:
        print("\n🎉 模型下载完成！现在可以运行DreamGaussian了。")
        print("💡 运行命令: python main.py --config configs/image.yaml input=data/anya_rgba.png save_path=anya")
    else:
        print("\n❌ 模型下载未完全成功，请检查网络连接后重新运行。")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
