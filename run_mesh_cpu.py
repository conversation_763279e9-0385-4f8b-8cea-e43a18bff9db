#!/usr/bin/env python3
"""
使用CPU模式运行DreamGaussian第二阶段
"""

import os
import sys

def patch_nvdiffrast_for_cpu():
    """修补nvdiffrast以使用CPU模式"""
    print("🔧 配置nvdiffrast使用CPU模式...")
    
    # 设置环境变量强制使用CPU
    os.environ['NVDIFFRAST_FORCE_CPU'] = '1'
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    
    print("✅ 已设置CPU模式环境变量")

def run_mesh_stage_cpu():
    """使用CPU模式运行网格生成"""
    import subprocess
    
    print("🚀 使用CPU模式运行第二阶段...")
    
    # 设置CPU模式
    patch_nvdiffrast_for_cpu()
    
    # 运行命令
    cmd = [
        ".venv\\Scripts\\python.exe",
        "main2.py",
        "--config", "configs/image.yaml",
        "input=data/anya_rgba.png", 
        "save_path=anya",
        "mesh_format=obj",
        "--force_cuda_rast=false"  # 强制不使用CUDA光栅化
    ]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    
    try:
        # 使用当前环境运行
        env = os.environ.copy()
        env['NVDIFFRAST_FORCE_CPU'] = '1'
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=1800)
        
        print("📤 输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"💥 运行失败: {e}")
        return False

if __name__ == '__main__':
    print("🎯 DreamGaussian CPU模式运行器")
    print("=" * 50)
    
    success = run_mesh_stage_cpu()
    if success:
        print("🎉 CPU模式运行成功！")
    else:
        print("❌ CPU模式运行失败")
    
    sys.exit(0 if success else 1)
