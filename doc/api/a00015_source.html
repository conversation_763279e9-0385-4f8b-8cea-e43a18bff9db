<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: common.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">common.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00015.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;detail/_fixes.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;{</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00241.html#ga81d3abddd0ef0c8de579bc541ecadab6">abs</a>(genType x);</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga81d3abddd0ef0c8de579bc541ecadab6">abs</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga1e2e5cfff800056540e32f6c9b604b28">sign</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d">floor</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaf9375e3e06173271d49e6ffa3a334259">trunc</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gafa03aca8c4713e1cc892aa92ca135a7e">round</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga76b81785045a057989a84d99aeeb1578">roundEven</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gafb9d2a645a23aca12d4d6de0104b7657">ceil</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#ga2df623004f634b440d61e018d62c751b">fract</a>(genType x);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga2df623004f634b440d61e018d62c751b">fract</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#ga9b197a452cd52db3c5c18bac72bd7798">mod</a>(genType x, genType y);</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga9b197a452cd52db3c5c18bac72bd7798">mod</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, T y);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga9b197a452cd52db3c5c18bac72bd7798">mod</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#ga85e33f139b8db1b39b590a5713b9e679">modf</a>(genType x, genType&amp; i);</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48">min</a>(genType x, genType y);</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48">min</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, T y);</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48">min</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00241.html#gac1fec0c3303b572a6d4697a637213870">max</a>(genType x, genType y);</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gac1fec0c3303b572a6d4697a637213870">max</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, T y);</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gac1fec0c3303b572a6d4697a637213870">max</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00241.html#gaa0f2f12e9108b09e22a3f0b2008a0b5d">clamp</a>(genType x, genType minVal, genType maxVal);</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaa0f2f12e9108b09e22a3f0b2008a0b5d">clamp</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, T minVal, T maxVal);</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaa0f2f12e9108b09e22a3f0b2008a0b5d">clamp</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; minVal, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; maxVal);</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genTypeT, <span class="keyword">typename</span> genTypeU&gt;</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;        GLM_FUNC_DECL genTypeT <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(genTypeT x, genTypeT y, genTypeU a);</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, U a);</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f">step</a>(genType edge, genType x);</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f">step</a>(T edge, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f">step</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; edge, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#ga562edf7eca082cc5b7a0aaf180436daf">smoothstep</a>(genType edge0, genType edge1, genType x);</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga562edf7eca082cc5b7a0aaf180436daf">smoothstep</a>(T edge0, T edge1, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#ga562edf7eca082cc5b7a0aaf180436daf">smoothstep</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; edge0, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; edge1, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;        GLM_FUNC_DECL vec&lt;L, bool, Q&gt; <a class="code" href="a00241.html#ga29ef934c00306490de837b4746b4e14d">isnan</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;        GLM_FUNC_DECL vec&lt;L, bool, Q&gt; <a class="code" href="a00241.html#ga2885587c23a106301f20443896365b62">isinf</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">int</span> <a class="code" href="a00241.html#ga99f7d62f78ac5ea3b49bae715c9488ed">floatBitsToInt</a>(<span class="keywordtype">float</span> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;        GLM_FUNC_DECL vec&lt;L, int, Q&gt; <a class="code" href="a00241.html#ga99f7d62f78ac5ea3b49bae715c9488ed">floatBitsToInt</a>(vec&lt;L, float, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00241.html#ga49418ba4c8a60fbbb5d57b705f3e26db">floatBitsToUint</a>(<span class="keywordtype">float</span> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;        GLM_FUNC_DECL vec&lt;L, uint, Q&gt; <a class="code" href="a00241.html#ga49418ba4c8a60fbbb5d57b705f3e26db">floatBitsToUint</a>(vec&lt;L, float, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0">intBitsToFloat</a>(<span class="keywordtype">int</span> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;        GLM_FUNC_DECL vec&lt;L, float, Q&gt; <a class="code" href="a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0">intBitsToFloat</a>(vec&lt;L, int, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00241.html#ga97f46b5f7b42fe44482e13356eb394ae">uintBitsToFloat</a>(uint <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;        GLM_FUNC_DECL vec&lt;L, float, Q&gt; <a class="code" href="a00241.html#ga97f46b5f7b42fe44482e13356eb394ae">uintBitsToFloat</a>(vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#gad0f444d4b81cc53c3b6edf5aa25078c2">fma</a>(genType <span class="keyword">const</span>&amp; a, genType <span class="keyword">const</span>&amp; b, genType <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#gaddf5ef73283c171730e0bcc11833fa81">frexp</a>(genType x, <span class="keywordtype">int</span>&amp; <a class="code" href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">exp</a>);</div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;        </div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gaddf5ef73283c171730e0bcc11833fa81">frexp</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, int, Q&gt;&amp; <a class="code" href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">exp</a>);</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;</div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00241.html#gac3010e0a0c35a1b514540f2fb579c58c">ldexp</a>(genType <span class="keyword">const</span>&amp; x, <span class="keywordtype">int</span> <span class="keyword">const</span>&amp; <a class="code" href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">exp</a>);</div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;        </div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00241.html#gac3010e0a0c35a1b514540f2fb579c58c">ldexp</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, int, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">exp</a>);</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;</div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;<span class="preprocessor">#include &quot;detail/func_common.inl&quot;</span></div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;</div>
<div class="ttc" id="a00241_html_gaa9d0742639e85b29c7c5de11cfd6840d"><div class="ttname"><a href="a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d">glm::floor</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; floor(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a value equal to the nearest integer that is less then or equal to x. </div></div>
<div class="ttc" id="a00241_html_gad0f444d4b81cc53c3b6edf5aa25078c2"><div class="ttname"><a href="a00241.html#gad0f444d4b81cc53c3b6edf5aa25078c2">glm::fma</a></div><div class="ttdeci">GLM_FUNC_DECL genType fma(genType const &amp;a, genType const &amp;b, genType const &amp;c)</div><div class="ttdoc">Computes and returns a * b + c. </div></div>
<div class="ttc" id="a00241_html_gaf9375e3e06173271d49e6ffa3a334259"><div class="ttname"><a href="a00241.html#gaf9375e3e06173271d49e6ffa3a334259">glm::trunc</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; trunc(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a value equal to the nearest integer to x whose absolute value is not larger than the absolut...</div></div>
<div class="ttc" id="a00241_html_ga9b197a452cd52db3c5c18bac72bd7798"><div class="ttname"><a href="a00241.html#ga9b197a452cd52db3c5c18bac72bd7798">glm::mod</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; mod(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Modulus. </div></div>
<div class="ttc" id="a00241_html_gaa0f2f12e9108b09e22a3f0b2008a0b5d"><div class="ttname"><a href="a00241.html#gaa0f2f12e9108b09e22a3f0b2008a0b5d">glm::clamp</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; clamp(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;minVal, vec&lt; L, T, Q &gt; const &amp;maxVal)</div><div class="ttdoc">Returns min(max(x, minVal), maxVal) for each component in x using the floating-point values minVal an...</div></div>
<div class="ttc" id="a00241_html_gafa03aca8c4713e1cc892aa92ca135a7e"><div class="ttname"><a href="a00241.html#gafa03aca8c4713e1cc892aa92ca135a7e">glm::round</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; round(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a value equal to the nearest integer to x. </div></div>
<div class="ttc" id="a00241_html_ga97f46b5f7b42fe44482e13356eb394ae"><div class="ttname"><a href="a00241.html#ga97f46b5f7b42fe44482e13356eb394ae">glm::uintBitsToFloat</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, float, Q &gt; uintBitsToFloat(vec&lt; L, uint, Q &gt; const &amp;v)</div><div class="ttdoc">Returns a floating-point value corresponding to a unsigned integer encoding of a floating-point value...</div></div>
<div class="ttc" id="a00241_html_ga1e2e5cfff800056540e32f6c9b604b28"><div class="ttname"><a href="a00241.html#ga1e2e5cfff800056540e32f6c9b604b28">glm::sign</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; sign(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns 1.0 if x > 0, 0.0 if x == 0, or -1.0 if x < 0. </div></div>
<div class="ttc" id="a00241_html_ga2885587c23a106301f20443896365b62"><div class="ttname"><a href="a00241.html#ga2885587c23a106301f20443896365b62">glm::isinf</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, bool, Q &gt; isinf(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns true if x holds a positive infinity or negative infinity representation in the underlying imp...</div></div>
<div class="ttc" id="a00241_html_ga76b81785045a057989a84d99aeeb1578"><div class="ttname"><a href="a00241.html#ga76b81785045a057989a84d99aeeb1578">glm::roundEven</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; roundEven(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a value equal to the nearest integer to x. </div></div>
<div class="ttc" id="a00241_html_ga85e33f139b8db1b39b590a5713b9e679"><div class="ttname"><a href="a00241.html#ga85e33f139b8db1b39b590a5713b9e679">glm::modf</a></div><div class="ttdeci">GLM_FUNC_DECL genType modf(genType x, genType &amp;i)</div><div class="ttdoc">Returns the fractional part of x and sets i to the integer part (as a whole number floating point val...</div></div>
<div class="ttc" id="a00241_html_gafb9d2a645a23aca12d4d6de0104b7657"><div class="ttname"><a href="a00241.html#gafb9d2a645a23aca12d4d6de0104b7657">glm::ceil</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; ceil(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a value equal to the nearest integer that is greater than or equal to x. </div></div>
<div class="ttc" id="a00241_html_ga31f49ef9e7d1beb003160c5e009b0c48"><div class="ttname"><a href="a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48">glm::min</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; min(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns y if y < x; otherwise, it returns x. </div></div>
<div class="ttc" id="a00241_html_ga7a0a8291a1cf3e1c2aee33030a1bd7b0"><div class="ttname"><a href="a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0">glm::intBitsToFloat</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, float, Q &gt; intBitsToFloat(vec&lt; L, int, Q &gt; const &amp;v)</div><div class="ttdoc">Returns a floating-point value corresponding to a signed integer encoding of a floating-point value...</div></div>
<div class="ttc" id="a00241_html_ga29ef934c00306490de837b4746b4e14d"><div class="ttname"><a href="a00241.html#ga29ef934c00306490de837b4746b4e14d">glm::isnan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, bool, Q &gt; isnan(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns true if x holds a NaN (not a number) representation in the underlying implementation&#39;s set of...</div></div>
<div class="ttc" id="a00242_html_ga071566cadc7505455e611f2a0353f4d4"><div class="ttname"><a href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">glm::exp</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; exp(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the natural exponentiation of x, i.e., e^x. </div></div>
<div class="ttc" id="a00241_html_ga49418ba4c8a60fbbb5d57b705f3e26db"><div class="ttname"><a href="a00241.html#ga49418ba4c8a60fbbb5d57b705f3e26db">glm::floatBitsToUint</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, uint, Q &gt; floatBitsToUint(vec&lt; L, float, Q &gt; const &amp;v)</div><div class="ttdoc">Returns a unsigned integer value representing the encoding of a floating-point value. </div></div>
<div class="ttc" id="a00241_html_ga562edf7eca082cc5b7a0aaf180436daf"><div class="ttname"><a href="a00241.html#ga562edf7eca082cc5b7a0aaf180436daf">glm::smoothstep</a></div><div class="ttdeci">GLM_FUNC_DECL genType smoothstep(genType edge0, genType edge1, genType x)</div><div class="ttdoc">Returns 0.0 if x <= edge0 and 1.0 if x >= edge1 and performs smooth Hermite interpolation between 0 a...</div></div>
<div class="ttc" id="a00241_html_ga81d3abddd0ef0c8de579bc541ecadab6"><div class="ttname"><a href="a00241.html#ga81d3abddd0ef0c8de579bc541ecadab6">glm::abs</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; abs(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns x if x >= 0; otherwise, it returns -x. </div></div>
<div class="ttc" id="a00241_html_gac1fec0c3303b572a6d4697a637213870"><div class="ttname"><a href="a00241.html#gac1fec0c3303b572a6d4697a637213870">glm::max</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; max(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns y if x < y; otherwise, it returns x. </div></div>
<div class="ttc" id="a00241_html_gaf4a5fc81619c7d3e8b22f53d4a098c7f"><div class="ttname"><a href="a00241.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f">glm::step</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; step(vec&lt; L, T, Q &gt; const &amp;edge, vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns 0.0 if x < edge, otherwise it returns 1.0. </div></div>
<div class="ttc" id="a00241_html_ga2df623004f634b440d61e018d62c751b"><div class="ttname"><a href="a00241.html#ga2df623004f634b440d61e018d62c751b">glm::fract</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; fract(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Return x - floor(x). </div></div>
<div class="ttc" id="a00241_html_gac3010e0a0c35a1b514540f2fb579c58c"><div class="ttname"><a href="a00241.html#gac3010e0a0c35a1b514540f2fb579c58c">glm::ldexp</a></div><div class="ttdeci">GLM_FUNC_DECL genType ldexp(genType const &amp;x, int const &amp;exp)</div><div class="ttdoc">Builds a floating-point number from x and the corresponding integral exponent of two in exp...</div></div>
<div class="ttc" id="a00241_html_ga99f7d62f78ac5ea3b49bae715c9488ed"><div class="ttname"><a href="a00241.html#ga99f7d62f78ac5ea3b49bae715c9488ed">glm::floatBitsToInt</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, int, Q &gt; floatBitsToInt(vec&lt; L, float, Q &gt; const &amp;v)</div><div class="ttdoc">Returns a signed integer value representing the encoding of a floating-point value. </div></div>
<div class="ttc" id="a00241_html_ga8e93f374aae27d1a88b921860351f8d4"><div class="ttname"><a href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">glm::mix</a></div><div class="ttdeci">GLM_FUNC_DECL genTypeT mix(genTypeT x, genTypeT y, genTypeU a)</div><div class="ttdoc">If genTypeU is a floating scalar or vector: Returns x * (1.0 - a) + y * a, i.e., the linear blend of ...</div></div>
<div class="ttc" id="a00241_html_gaddf5ef73283c171730e0bcc11833fa81"><div class="ttname"><a href="a00241.html#gaddf5ef73283c171730e0bcc11833fa81">glm::frexp</a></div><div class="ttdeci">GLM_FUNC_DECL genType frexp(genType x, int &amp;exp)</div><div class="ttdoc">Splits x into a floating-point significand in the range [0.5, 1.0) and an integral exponent of two...</div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
