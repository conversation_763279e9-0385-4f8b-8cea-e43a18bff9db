<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_float4x4_precision.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_float4x4_precision.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00099.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat4x4.hpp&quot;</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;</div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;{</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="a00284.html#ga2dedee030500865267cd5851c00c139d">   16</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, lowp&gt;          <a class="code" href="a00284.html#ga2dedee030500865267cd5851c00c139d">lowp_mat4</a>;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno"><a class="line" href="a00284.html#gab8531bc3f269aa45835cd6e1972b7fc7">   22</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, mediump&gt;       <a class="code" href="a00284.html#gab8531bc3f269aa45835cd6e1972b7fc7">mediump_mat4</a>;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno"><a class="line" href="a00284.html#gad72e13d669d039f12ae5afa23148adc1">   28</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, highp&gt;         <a class="code" href="a00284.html#gad72e13d669d039f12ae5afa23148adc1">highp_mat4</a>;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="a00284.html#ga686468a9a815bd4db8cddae42a6d6b87">   34</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, lowp&gt;          <a class="code" href="a00284.html#ga686468a9a815bd4db8cddae42a6d6b87">lowp_mat4x4</a>;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="a00284.html#ga15bca2b70917d9752231160d9da74b01">   40</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, mediump&gt;       <a class="code" href="a00284.html#ga15bca2b70917d9752231160d9da74b01">mediump_mat4x4</a>;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00284.html#ga58cc504be0e3b61c48bc91554a767b9f">   46</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, highp&gt;         <a class="code" href="a00284.html#ga58cc504be0e3b61c48bc91554a767b9f">highp_mat4x4</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="ttc" id="a00284_html_ga15bca2b70917d9752231160d9da74b01"><div class="ttname"><a href="a00284.html#ga15bca2b70917d9752231160d9da74b01">glm::mediump_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, mediump &gt; mediump_mat4x4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers using medium precision ar...</div><div class="ttdef"><b>Definition:</b> <a href="a00099_source.html#l00040">matrix_float4x4_precision.hpp:40</a></div></div>
<div class="ttc" id="a00284_html_ga2dedee030500865267cd5851c00c139d"><div class="ttname"><a href="a00284.html#ga2dedee030500865267cd5851c00c139d">glm::lowp_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, lowp &gt; lowp_mat4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers using low precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00099_source.html#l00016">matrix_float4x4_precision.hpp:16</a></div></div>
<div class="ttc" id="a00284_html_gab8531bc3f269aa45835cd6e1972b7fc7"><div class="ttname"><a href="a00284.html#gab8531bc3f269aa45835cd6e1972b7fc7">glm::mediump_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, mediump &gt; mediump_mat4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers using medium precision ar...</div><div class="ttdef"><b>Definition:</b> <a href="a00099_source.html#l00022">matrix_float4x4_precision.hpp:22</a></div></div>
<div class="ttc" id="a00284_html_ga58cc504be0e3b61c48bc91554a767b9f"><div class="ttname"><a href="a00284.html#ga58cc504be0e3b61c48bc91554a767b9f">glm::highp_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, highp &gt; highp_mat4x4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers using high precision arit...</div><div class="ttdef"><b>Definition:</b> <a href="a00099_source.html#l00046">matrix_float4x4_precision.hpp:46</a></div></div>
<div class="ttc" id="a00284_html_ga686468a9a815bd4db8cddae42a6d6b87"><div class="ttname"><a href="a00284.html#ga686468a9a815bd4db8cddae42a6d6b87">glm::lowp_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, lowp &gt; lowp_mat4x4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers using low precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00099_source.html#l00034">matrix_float4x4_precision.hpp:34</a></div></div>
<div class="ttc" id="a00284_html_gad72e13d669d039f12ae5afa23148adc1"><div class="ttname"><a href="a00284.html#gad72e13d669d039f12ae5afa23148adc1">glm::highp_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, highp &gt; highp_mat4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers using high precision arit...</div><div class="ttdef"><b>Definition:</b> <a href="a00099_source.html#l00028">matrix_float4x4_precision.hpp:28</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
