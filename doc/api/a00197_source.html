<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_common.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">vector_common.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00197.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_common.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_vector_common extension included&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;{</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#gab66920ed064ab518d6859c5a889c4be4">min</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#gab66920ed064ab518d6859c5a889c4be4">min</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; c, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga94d42b8da2b4ded5ddf7504fbdc6bf10">max</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z);</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga94d42b8da2b4ded5ddf7504fbdc6bf10">max</a>( vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; w);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">fmin</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, T y);</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">fmin</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">fmin</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">fmin</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; c, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">fmax</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, T b);</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">fmax</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b);</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">fmax</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">fmax</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; a, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; b, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; c, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#include &quot;vector_common.inl&quot;</span></div>
<div class="ttc" id="a00267_html_ga4ed3eb250ccbe17bfe8ded8a6b72d230"><div class="ttname"><a href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">glm::fmax</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; fmax(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)</div><div class="ttdoc">Returns y if x < y; otherwise, it returns x. </div></div>
<div class="ttc" id="a00267_html_ga4a543dd7d22ad1f3b8b839f808a9d93c"><div class="ttname"><a href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">glm::fmin</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; fmin(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)</div><div class="ttdoc">Returns y if y < x; otherwise, it returns x. </div></div>
<div class="ttc" id="a00267_html_ga94d42b8da2b4ded5ddf7504fbdc6bf10"><div class="ttname"><a href="a00267.html#ga94d42b8da2b4ded5ddf7504fbdc6bf10">glm::max</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; max(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, T, Q &gt; const &amp;w)</div><div class="ttdoc">Return the maximum component-wise values of 4 inputs. </div></div>
<div class="ttc" id="a00267_html_gab66920ed064ab518d6859c5a889c4be4"><div class="ttname"><a href="a00267.html#gab66920ed064ab518d6859c5a889c4be4">glm::min</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; min(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)</div><div class="ttdoc">Return the minimum component-wise values of 4 inputs. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
