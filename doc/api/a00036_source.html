<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: geometric.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">geometric.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00036.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00180.html">detail/type_vec3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;{</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00279.html#ga0cdabbb000834d994a1d6dc56f8f5263">length</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00279.html#gaa68de6c53e20dfb2dac2d20197562e3f">distance</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p0, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p1);</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00279.html#gaad6c5d9d39bdc0bf43baf1b22e147a0a">dot</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00279.html#gaeeec0794212fe84fc9d261de067c9587">cross</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00279.html#ga3b8d3dcae77870781392ed2902cce597">normalize</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00279.html#ga7aed0a36c738169402404a3a5d54e43b">faceforward</a>(</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; N,</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; I,</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Nref);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00279.html#ga5631dd1d5618de5450b1ea3cf3e94905">reflect</a>(</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; I,</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; N);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00279.html#ga01da3dff9e2ef6b9d4915c3047e22b74">refract</a>(</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; I,</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; N,</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                T eta);</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="preprocessor">#include &quot;detail/func_geometric.inl&quot;</span></div>
<div class="ttc" id="a00279_html_ga5631dd1d5618de5450b1ea3cf3e94905"><div class="ttname"><a href="a00279.html#ga5631dd1d5618de5450b1ea3cf3e94905">glm::reflect</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; reflect(vec&lt; L, T, Q &gt; const &amp;I, vec&lt; L, T, Q &gt; const &amp;N)</div><div class="ttdoc">For the incident vector I and surface orientation N, returns the reflection direction : result = I - ...</div></div>
<div class="ttc" id="a00279_html_ga7aed0a36c738169402404a3a5d54e43b"><div class="ttname"><a href="a00279.html#ga7aed0a36c738169402404a3a5d54e43b">glm::faceforward</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; faceforward(vec&lt; L, T, Q &gt; const &amp;N, vec&lt; L, T, Q &gt; const &amp;I, vec&lt; L, T, Q &gt; const &amp;Nref)</div><div class="ttdoc">If dot(Nref, I) < 0.0, return N, otherwise, return -N. </div></div>
<div class="ttc" id="a00279_html_ga0cdabbb000834d994a1d6dc56f8f5263"><div class="ttname"><a href="a00279.html#ga0cdabbb000834d994a1d6dc56f8f5263">glm::length</a></div><div class="ttdeci">GLM_FUNC_DECL T length(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the length of x, i.e., sqrt(x * x). </div></div>
<div class="ttc" id="a00279_html_gaeeec0794212fe84fc9d261de067c9587"><div class="ttname"><a href="a00279.html#gaeeec0794212fe84fc9d261de067c9587">glm::cross</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; cross(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the cross product of x and y. </div></div>
<div class="ttc" id="a00279_html_ga01da3dff9e2ef6b9d4915c3047e22b74"><div class="ttname"><a href="a00279.html#ga01da3dff9e2ef6b9d4915c3047e22b74">glm::refract</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; refract(vec&lt; L, T, Q &gt; const &amp;I, vec&lt; L, T, Q &gt; const &amp;N, T eta)</div><div class="ttdoc">For the incident vector I and surface normal N, and the ratio of indices of refraction eta...</div></div>
<div class="ttc" id="a00279_html_ga3b8d3dcae77870781392ed2902cce597"><div class="ttname"><a href="a00279.html#ga3b8d3dcae77870781392ed2902cce597">glm::normalize</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; normalize(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a vector in the same direction as x but with length of 1. </div></div>
<div class="ttc" id="a00180_html"><div class="ttname"><a href="a00180.html">type_vec3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00279_html_gaa68de6c53e20dfb2dac2d20197562e3f"><div class="ttname"><a href="a00279.html#gaa68de6c53e20dfb2dac2d20197562e3f">glm::distance</a></div><div class="ttdeci">GLM_FUNC_DECL T distance(vec&lt; L, T, Q &gt; const &amp;p0, vec&lt; L, T, Q &gt; const &amp;p1)</div><div class="ttdoc">Returns the distance betwwen p0 and p1, i.e., length(p0 - p1). </div></div>
<div class="ttc" id="a00279_html_gaad6c5d9d39bdc0bf43baf1b22e147a0a"><div class="ttname"><a href="a00279.html#gaad6c5d9d39bdc0bf43baf1b22e147a0a">glm::dot</a></div><div class="ttdeci">GLM_FUNC_DECL T dot(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the dot product of x and y, i.e., result = x * y. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
