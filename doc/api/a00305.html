<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_type_ptr</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_type_ptr<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00175.html" title="GLM_GTC_type_ptr ">glm/gtc/type_ptr.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga04409e74dc3da251d2501acf5b4b546c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga04409e74dc3da251d2501acf5b4b546c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga04409e74dc3da251d2501acf5b4b546c">make_mat2</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga04409e74dc3da251d2501acf5b4b546c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga04409e74dc3da251d2501acf5b4b546c">More...</a><br /></td></tr>
<tr class="separator:ga04409e74dc3da251d2501acf5b4b546c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae49e1c7bcd5abec74d1c34155031f663"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae49e1c7bcd5abec74d1c34155031f663"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gae49e1c7bcd5abec74d1c34155031f663">make_mat2x2</a> (T const *const ptr)</td></tr>
<tr class="memdesc:gae49e1c7bcd5abec74d1c34155031f663"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#gae49e1c7bcd5abec74d1c34155031f663">More...</a><br /></td></tr>
<tr class="separator:gae49e1c7bcd5abec74d1c34155031f663"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga21982104164789cf8985483aaefc25e8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga21982104164789cf8985483aaefc25e8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga21982104164789cf8985483aaefc25e8">make_mat2x3</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga21982104164789cf8985483aaefc25e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga21982104164789cf8985483aaefc25e8">More...</a><br /></td></tr>
<tr class="separator:ga21982104164789cf8985483aaefc25e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga078b862c90b0e9a79ed43a58997d8388"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga078b862c90b0e9a79ed43a58997d8388"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga078b862c90b0e9a79ed43a58997d8388">make_mat2x4</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga078b862c90b0e9a79ed43a58997d8388"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga078b862c90b0e9a79ed43a58997d8388">More...</a><br /></td></tr>
<tr class="separator:ga078b862c90b0e9a79ed43a58997d8388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga611ee7c4d4cadfc83a8fa8e1d10a170f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga611ee7c4d4cadfc83a8fa8e1d10a170f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga611ee7c4d4cadfc83a8fa8e1d10a170f">make_mat3</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga611ee7c4d4cadfc83a8fa8e1d10a170f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga611ee7c4d4cadfc83a8fa8e1d10a170f">More...</a><br /></td></tr>
<tr class="separator:ga611ee7c4d4cadfc83a8fa8e1d10a170f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27a24e121dc39e6857620e0f85b6e1a8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga27a24e121dc39e6857620e0f85b6e1a8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga27a24e121dc39e6857620e0f85b6e1a8">make_mat3x2</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga27a24e121dc39e6857620e0f85b6e1a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga27a24e121dc39e6857620e0f85b6e1a8">More...</a><br /></td></tr>
<tr class="separator:ga27a24e121dc39e6857620e0f85b6e1a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2e8337b15c3362aaeb6e5849e1c0536"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf2e8337b15c3362aaeb6e5849e1c0536"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gaf2e8337b15c3362aaeb6e5849e1c0536">make_mat3x3</a> (T const *const ptr)</td></tr>
<tr class="memdesc:gaf2e8337b15c3362aaeb6e5849e1c0536"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#gaf2e8337b15c3362aaeb6e5849e1c0536">More...</a><br /></td></tr>
<tr class="separator:gaf2e8337b15c3362aaeb6e5849e1c0536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05dd66232aedb993e3b8e7b35eaf932b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga05dd66232aedb993e3b8e7b35eaf932b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga05dd66232aedb993e3b8e7b35eaf932b">make_mat3x4</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga05dd66232aedb993e3b8e7b35eaf932b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga05dd66232aedb993e3b8e7b35eaf932b">More...</a><br /></td></tr>
<tr class="separator:ga05dd66232aedb993e3b8e7b35eaf932b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7bcedb710d1446c87fd1fc93ed8ee9a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae7bcedb710d1446c87fd1fc93ed8ee9a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gae7bcedb710d1446c87fd1fc93ed8ee9a">make_mat4</a> (T const *const ptr)</td></tr>
<tr class="memdesc:gae7bcedb710d1446c87fd1fc93ed8ee9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#gae7bcedb710d1446c87fd1fc93ed8ee9a">More...</a><br /></td></tr>
<tr class="separator:gae7bcedb710d1446c87fd1fc93ed8ee9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b34c9b25bf3310d8ff9c828c7e2d97c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga8b34c9b25bf3310d8ff9c828c7e2d97c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga8b34c9b25bf3310d8ff9c828c7e2d97c">make_mat4x2</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga8b34c9b25bf3310d8ff9c828c7e2d97c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga8b34c9b25bf3310d8ff9c828c7e2d97c">More...</a><br /></td></tr>
<tr class="separator:ga8b34c9b25bf3310d8ff9c828c7e2d97c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0330bf6640092d7985fac92927bbd42b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0330bf6640092d7985fac92927bbd42b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga0330bf6640092d7985fac92927bbd42b">make_mat4x3</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga0330bf6640092d7985fac92927bbd42b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga0330bf6640092d7985fac92927bbd42b">More...</a><br /></td></tr>
<tr class="separator:ga0330bf6640092d7985fac92927bbd42b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f084be30e404844bfbb4a551ac2728c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga8f084be30e404844bfbb4a551ac2728c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga8f084be30e404844bfbb4a551ac2728c">make_mat4x4</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga8f084be30e404844bfbb4a551ac2728c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from a pointer.  <a href="a00305.html#ga8f084be30e404844bfbb4a551ac2728c">More...</a><br /></td></tr>
<tr class="separator:ga8f084be30e404844bfbb4a551ac2728c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga58110d7d81cf7d029e2bab7f8cd9b246"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga58110d7d81cf7d029e2bab7f8cd9b246"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga58110d7d81cf7d029e2bab7f8cd9b246">make_quat</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga58110d7d81cf7d029e2bab7f8cd9b246"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a quaternion from a pointer.  <a href="a00305.html#ga58110d7d81cf7d029e2bab7f8cd9b246">More...</a><br /></td></tr>
<tr class="separator:ga58110d7d81cf7d029e2bab7f8cd9b246"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4135f03f3049f0a4eb76545c4967957c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4135f03f3049f0a4eb76545c4967957c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 1, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga4135f03f3049f0a4eb76545c4967957c">make_vec1</a> (vec&lt; 1, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga4135f03f3049f0a4eb76545c4967957c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga4135f03f3049f0a4eb76545c4967957c">More...</a><br /></td></tr>
<tr class="separator:ga4135f03f3049f0a4eb76545c4967957c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13c92b81e55f201b052a6404d57da220"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga13c92b81e55f201b052a6404d57da220"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 1, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga13c92b81e55f201b052a6404d57da220">make_vec1</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga13c92b81e55f201b052a6404d57da220"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga13c92b81e55f201b052a6404d57da220">More...</a><br /></td></tr>
<tr class="separator:ga13c92b81e55f201b052a6404d57da220"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c23cc74086d361e22bbd5e91a334e03"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3c23cc74086d361e22bbd5e91a334e03"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 1, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga3c23cc74086d361e22bbd5e91a334e03">make_vec1</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga3c23cc74086d361e22bbd5e91a334e03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga3c23cc74086d361e22bbd5e91a334e03">More...</a><br /></td></tr>
<tr class="separator:ga3c23cc74086d361e22bbd5e91a334e03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6af06bb60d64ca8bcd169e3c93bc2419"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6af06bb60d64ca8bcd169e3c93bc2419"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 1, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">make_vec1</a> (vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga6af06bb60d64ca8bcd169e3c93bc2419"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">More...</a><br /></td></tr>
<tr class="separator:ga6af06bb60d64ca8bcd169e3c93bc2419"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8476d0e6f1b9b4a6193cc25f59d8a896"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8476d0e6f1b9b4a6193cc25f59d8a896"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga8476d0e6f1b9b4a6193cc25f59d8a896">make_vec2</a> (vec&lt; 1, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga8476d0e6f1b9b4a6193cc25f59d8a896"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga8476d0e6f1b9b4a6193cc25f59d8a896">More...</a><br /></td></tr>
<tr class="separator:ga8476d0e6f1b9b4a6193cc25f59d8a896"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae54bd325a08ad26edf63929201adebc7"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae54bd325a08ad26edf63929201adebc7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gae54bd325a08ad26edf63929201adebc7">make_vec2</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gae54bd325a08ad26edf63929201adebc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#gae54bd325a08ad26edf63929201adebc7">More...</a><br /></td></tr>
<tr class="separator:gae54bd325a08ad26edf63929201adebc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0084fea4694cf47276e9cccbe7b1015a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0084fea4694cf47276e9cccbe7b1015a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga0084fea4694cf47276e9cccbe7b1015a">make_vec2</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga0084fea4694cf47276e9cccbe7b1015a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga0084fea4694cf47276e9cccbe7b1015a">More...</a><br /></td></tr>
<tr class="separator:ga0084fea4694cf47276e9cccbe7b1015a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b81f71f3a222fe5bba81e3983751249"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2b81f71f3a222fe5bba81e3983751249"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga2b81f71f3a222fe5bba81e3983751249">make_vec2</a> (vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga2b81f71f3a222fe5bba81e3983751249"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga2b81f71f3a222fe5bba81e3983751249">More...</a><br /></td></tr>
<tr class="separator:ga2b81f71f3a222fe5bba81e3983751249"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81253cf7b0ebfbb1e70540c5774e6824"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga81253cf7b0ebfbb1e70540c5774e6824"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">make_vec2</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga81253cf7b0ebfbb1e70540c5774e6824"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">More...</a><br /></td></tr>
<tr class="separator:ga81253cf7b0ebfbb1e70540c5774e6824"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9147e4b3a5d0f4772edfbfd179d7ea0b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga9147e4b3a5d0f4772edfbfd179d7ea0b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga9147e4b3a5d0f4772edfbfd179d7ea0b">make_vec3</a> (vec&lt; 1, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga9147e4b3a5d0f4772edfbfd179d7ea0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga9147e4b3a5d0f4772edfbfd179d7ea0b">More...</a><br /></td></tr>
<tr class="separator:ga9147e4b3a5d0f4772edfbfd179d7ea0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga482b60a842a5b154d3eed392417a9511"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga482b60a842a5b154d3eed392417a9511"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga482b60a842a5b154d3eed392417a9511">make_vec3</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga482b60a842a5b154d3eed392417a9511"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga482b60a842a5b154d3eed392417a9511">More...</a><br /></td></tr>
<tr class="separator:ga482b60a842a5b154d3eed392417a9511"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacd57046034df557b8b1c457f58613623"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacd57046034df557b8b1c457f58613623"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gacd57046034df557b8b1c457f58613623">make_vec3</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gacd57046034df557b8b1c457f58613623"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#gacd57046034df557b8b1c457f58613623">More...</a><br /></td></tr>
<tr class="separator:gacd57046034df557b8b1c457f58613623"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b589ed7d41a298b516d2a69169248f1"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8b589ed7d41a298b516d2a69169248f1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga8b589ed7d41a298b516d2a69169248f1">make_vec3</a> (vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga8b589ed7d41a298b516d2a69169248f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga8b589ed7d41a298b516d2a69169248f1">More...</a><br /></td></tr>
<tr class="separator:ga8b589ed7d41a298b516d2a69169248f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad9e0d36ff489cb30c65ad1fa40351651"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gad9e0d36ff489cb30c65ad1fa40351651"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">make_vec3</a> (T const *const ptr)</td></tr>
<tr class="memdesc:gad9e0d36ff489cb30c65ad1fa40351651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">More...</a><br /></td></tr>
<tr class="separator:gad9e0d36ff489cb30c65ad1fa40351651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga600cb97f70c5d50d3a4a145e1cafbf37"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga600cb97f70c5d50d3a4a145e1cafbf37"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga600cb97f70c5d50d3a4a145e1cafbf37">make_vec4</a> (vec&lt; 1, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga600cb97f70c5d50d3a4a145e1cafbf37"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga600cb97f70c5d50d3a4a145e1cafbf37">More...</a><br /></td></tr>
<tr class="separator:ga600cb97f70c5d50d3a4a145e1cafbf37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa9bd116caf28196fd1cf00b278286fa7"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa9bd116caf28196fd1cf00b278286fa7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gaa9bd116caf28196fd1cf00b278286fa7">make_vec4</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaa9bd116caf28196fd1cf00b278286fa7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#gaa9bd116caf28196fd1cf00b278286fa7">More...</a><br /></td></tr>
<tr class="separator:gaa9bd116caf28196fd1cf00b278286fa7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4036328ba4702c74cbdfad1fc03d1b8f"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4036328ba4702c74cbdfad1fc03d1b8f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga4036328ba4702c74cbdfad1fc03d1b8f">make_vec4</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga4036328ba4702c74cbdfad1fc03d1b8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga4036328ba4702c74cbdfad1fc03d1b8f">More...</a><br /></td></tr>
<tr class="separator:ga4036328ba4702c74cbdfad1fc03d1b8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa95cb15732f708f613e65a0578895ae5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa95cb15732f708f613e65a0578895ae5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#gaa95cb15732f708f613e65a0578895ae5">make_vec4</a> (vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaa95cb15732f708f613e65a0578895ae5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#gaa95cb15732f708f613e65a0578895ae5">More...</a><br /></td></tr>
<tr class="separator:gaa95cb15732f708f613e65a0578895ae5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga63f576518993efc22a969f18f80e29bb"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga63f576518993efc22a969f18f80e29bb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga63f576518993efc22a969f18f80e29bb">make_vec4</a> (T const *const ptr)</td></tr>
<tr class="memdesc:ga63f576518993efc22a969f18f80e29bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a vector from a pointer.  <a href="a00305.html#ga63f576518993efc22a969f18f80e29bb">More...</a><br /></td></tr>
<tr class="separator:ga63f576518993efc22a969f18f80e29bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c64669e1ba1160ad9386e43dc57569a"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga1c64669e1ba1160ad9386e43dc57569a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType::value_type const *&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00305.html#ga1c64669e1ba1160ad9386e43dc57569a">value_ptr</a> (genType const &amp;v)</td></tr>
<tr class="memdesc:ga1c64669e1ba1160ad9386e43dc57569a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the constant address to the data of the input parameter.  <a href="a00305.html#ga1c64669e1ba1160ad9386e43dc57569a">More...</a><br /></td></tr>
<tr class="separator:ga1c64669e1ba1160ad9386e43dc57569a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00175.html" title="GLM_GTC_type_ptr ">glm/gtc/type_ptr.hpp</a>&gt; to use the features of this extension. </p>
<p>Handles the interaction between pointers and vector, matrix types.</p>
<p>This extension defines an overloaded function, glm::value_ptr. It returns a pointer to the memory layout of the object. Matrix types store their values in column-major order.</p>
<p>This is useful for uploading data to matrices or copying data to buffer objects.</p>
<p>Example: </p><div class="fragment"><div class="line"><span class="preprocessor">#include &lt;<a class="code" href="a00037.html">glm/glm.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="a00175.html">glm/gtc/type_ptr.hpp</a>&gt;</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">glm::vec3</a> aVector(3);</div>
<div class="line"><a class="code" href="a00283.html#ga0db98d836c5549d31cf64ecd043b7af7">glm::mat4</a> someMatrix(1.0);</div>
<div class="line"></div>
<div class="line">glUniform3fv(uniformLoc, 1, <a class="code" href="a00305.html#ga1c64669e1ba1160ad9386e43dc57569a">glm::value_ptr</a>(aVector));</div>
<div class="line">glUniformMatrix4fv(uniformMatrixLoc, 1, GL_FALSE, <a class="code" href="a00305.html#ga1c64669e1ba1160ad9386e43dc57569a">glm::value_ptr</a>(someMatrix));</div>
</div><!-- fragment --><p>&lt;<a class="el" href="a00175.html" title="GLM_GTC_type_ptr ">glm/gtc/type_ptr.hpp</a>&gt; need to be included to use the features of this extension. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga04409e74dc3da251d2501acf5b4b546c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 2, T, defaultp&gt; glm::make_mat2 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae49e1c7bcd5abec74d1c34155031f663"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 2, T, defaultp&gt; glm::make_mat2x2 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga21982104164789cf8985483aaefc25e8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 3, T, defaultp&gt; glm::make_mat2x3 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga078b862c90b0e9a79ed43a58997d8388"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 4, T, defaultp&gt; glm::make_mat2x4 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga611ee7c4d4cadfc83a8fa8e1d10a170f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, defaultp&gt; glm::make_mat3 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga27a24e121dc39e6857620e0f85b6e1a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 2, T, defaultp&gt; glm::make_mat3x2 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf2e8337b15c3362aaeb6e5849e1c0536"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, defaultp&gt; glm::make_mat3x3 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga05dd66232aedb993e3b8e7b35eaf932b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 4, T, defaultp&gt; glm::make_mat3x4 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae7bcedb710d1446c87fd1fc93ed8ee9a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::make_mat4 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8b34c9b25bf3310d8ff9c828c7e2d97c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 2, T, defaultp&gt; glm::make_mat4x2 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0330bf6640092d7985fac92927bbd42b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 3, T, defaultp&gt; glm::make_mat4x3 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8f084be30e404844bfbb4a551ac2728c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::make_mat4x4 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga58110d7d81cf7d029e2bab7f8cd9b246"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, defaultp&gt; glm::make_quat </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a quaternion from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4135f03f3049f0a4eb76545c4967957c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;1, T, Q&gt; glm::make_vec1 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 1, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga13c92b81e55f201b052a6404d57da220"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;1, T, Q&gt; glm::make_vec1 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3c23cc74086d361e22bbd5e91a334e03"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;1, T, Q&gt; glm::make_vec1 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6af06bb60d64ca8bcd169e3c93bc2419"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;1, T, Q&gt; glm::make_vec1 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8476d0e6f1b9b4a6193cc25f59d8a896"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, Q&gt; glm::make_vec2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 1, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae54bd325a08ad26edf63929201adebc7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, Q&gt; glm::make_vec2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0084fea4694cf47276e9cccbe7b1015a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, Q&gt; glm::make_vec2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2b81f71f3a222fe5bba81e3983751249"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, Q&gt; glm::make_vec2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga81253cf7b0ebfbb1e70540c5774e6824"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, defaultp&gt; glm::make_vec2 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9147e4b3a5d0f4772edfbfd179d7ea0b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::make_vec3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 1, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga482b60a842a5b154d3eed392417a9511"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::make_vec3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacd57046034df557b8b1c457f58613623"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::make_vec3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8b589ed7d41a298b516d2a69169248f1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::make_vec3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad9e0d36ff489cb30c65ad1fa40351651"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, defaultp&gt; glm::make_vec3 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga600cb97f70c5d50d3a4a145e1cafbf37"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::make_vec4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 1, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa9bd116caf28196fd1cf00b278286fa7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::make_vec4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4036328ba4702c74cbdfad1fc03d1b8f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::make_vec4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa95cb15732f708f613e65a0578895ae5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::make_vec4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga63f576518993efc22a969f18f80e29bb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, defaultp&gt; glm::make_vec4 </td>
          <td>(</td>
          <td class="paramtype">T const *const&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a vector from a pointer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1c64669e1ba1160ad9386e43dc57569a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType::value_type const* glm::value_ptr </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the constant address to the data of the input parameter. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00305.html" title="Include <glm/gtc/type_ptr.hpp> to use the features of this extension. ">GLM_GTC_type_ptr</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
