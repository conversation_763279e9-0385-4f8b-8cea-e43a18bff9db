<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_scalar_constants</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_scalar_constants<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides a list of constants and precomputed useful values.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2a1e57fc5592b69cfae84174cbfc9429"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga2a1e57fc5592b69cfae84174cbfc9429"></a>
template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga2a1e57fc5592b69cfae84174cbfc9429"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00259.html#ga2a1e57fc5592b69cfae84174cbfc9429">epsilon</a> ()</td></tr>
<tr class="memdesc:ga2a1e57fc5592b69cfae84174cbfc9429"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the epsilon constant for floating point types. <br /></td></tr>
<tr class="separator:ga2a1e57fc5592b69cfae84174cbfc9429"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94bafeb2a0f23ab6450fed1f98ee4e45"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga94bafeb2a0f23ab6450fed1f98ee4e45"></a>
template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga94bafeb2a0f23ab6450fed1f98ee4e45"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00259.html#ga94bafeb2a0f23ab6450fed1f98ee4e45">pi</a> ()</td></tr>
<tr class="memdesc:ga94bafeb2a0f23ab6450fed1f98ee4e45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the pi constant for floating point types. <br /></td></tr>
<tr class="separator:ga94bafeb2a0f23ab6450fed1f98ee4e45"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides a list of constants and precomputed useful values. </p>
<p>Include &lt;<a class="el" href="a00145.html" title="GLM_EXT_scalar_constants ">glm/ext/scalar_constants.hpp</a>&gt; to use the features of this extension. </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
