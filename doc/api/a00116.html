<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: number_precision.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">number_precision.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00346.html">GLM_GTX_number_precision</a>  
<a href="#details">More...</a></p>

<p><a href="a00116_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga145ad477a2a3e152855511c3b52469a6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga145ad477a2a3e152855511c3b52469a6"></a>
typedef f32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga145ad477a2a3e152855511c3b52469a6">f32mat1</a></td></tr>
<tr class="memdesc:ga145ad477a2a3e152855511c3b52469a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga145ad477a2a3e152855511c3b52469a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac88c6a4dbfc380aa26e3adbbade36348"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac88c6a4dbfc380aa26e3adbbade36348"></a>
typedef f32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#gac88c6a4dbfc380aa26e3adbbade36348">f32mat1x1</a></td></tr>
<tr class="memdesc:gac88c6a4dbfc380aa26e3adbbade36348"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:gac88c6a4dbfc380aa26e3adbbade36348"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07f8d7348eb7ae059a84c118fdfeb943"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga07f8d7348eb7ae059a84c118fdfeb943"></a>
typedef f32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga07f8d7348eb7ae059a84c118fdfeb943">f32vec1</a></td></tr>
<tr class="memdesc:ga07f8d7348eb7ae059a84c118fdfeb943"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga07f8d7348eb7ae059a84c118fdfeb943"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga59bfa589419b5265d01314fcecd33435"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga59bfa589419b5265d01314fcecd33435"></a>
typedef f64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga59bfa589419b5265d01314fcecd33435">f64mat1</a></td></tr>
<tr class="memdesc:ga59bfa589419b5265d01314fcecd33435"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga59bfa589419b5265d01314fcecd33435"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga448eeb08d0b7d8c43a8b292c981955fd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga448eeb08d0b7d8c43a8b292c981955fd"></a>
typedef f64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga448eeb08d0b7d8c43a8b292c981955fd">f64mat1x1</a></td></tr>
<tr class="memdesc:ga448eeb08d0b7d8c43a8b292c981955fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga448eeb08d0b7d8c43a8b292c981955fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5987a61b8c03d5c432a9e62f0b3efe1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae5987a61b8c03d5c432a9e62f0b3efe1"></a>
typedef f64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#gae5987a61b8c03d5c432a9e62f0b3efe1">f64vec1</a></td></tr>
<tr class="memdesc:gae5987a61b8c03d5c432a9e62f0b3efe1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:gae5987a61b8c03d5c432a9e62f0b3efe1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52cc069a92e126c3a8dcde93424d2ef0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga52cc069a92e126c3a8dcde93424d2ef0"></a>
typedef u16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga52cc069a92e126c3a8dcde93424d2ef0">u16vec1</a></td></tr>
<tr class="memdesc:ga52cc069a92e126c3a8dcde93424d2ef0"><td class="mdescLeft">&#160;</td><td class="mdescRight">16bit unsigned integer scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga52cc069a92e126c3a8dcde93424d2ef0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9bbc1e14aea65cba5e2dcfef6a67d9f3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9bbc1e14aea65cba5e2dcfef6a67d9f3"></a>
typedef u32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga9bbc1e14aea65cba5e2dcfef6a67d9f3">u32vec1</a></td></tr>
<tr class="memdesc:ga9bbc1e14aea65cba5e2dcfef6a67d9f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">32bit unsigned integer scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga9bbc1e14aea65cba5e2dcfef6a67d9f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga818de170e2584ab037130f2881925974"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga818de170e2584ab037130f2881925974"></a>
typedef u64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga818de170e2584ab037130f2881925974">u64vec1</a></td></tr>
<tr class="memdesc:ga818de170e2584ab037130f2881925974"><td class="mdescLeft">&#160;</td><td class="mdescRight">64bit unsigned integer scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga818de170e2584ab037130f2881925974"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5853fe457f4c8a6bc09343d0e9833980"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5853fe457f4c8a6bc09343d0e9833980"></a>
typedef u8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html#ga5853fe457f4c8a6bc09343d0e9833980">u8vec1</a></td></tr>
<tr class="memdesc:ga5853fe457f4c8a6bc09343d0e9833980"><td class="mdescLeft">&#160;</td><td class="mdescRight">8bit unsigned integer scalar. (from GLM_GTX_number_precision extension) <br /></td></tr>
<tr class="separator:ga5853fe457f4c8a6bc09343d0e9833980"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00346.html">GLM_GTX_number_precision</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00304.html" title="Include <glm/gtc/type_precision.hpp> to use the features of this extension. ">GLM_GTC_type_precision</a> (dependence) </dd>
<dd>
<a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00116_source.html">number_precision.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
