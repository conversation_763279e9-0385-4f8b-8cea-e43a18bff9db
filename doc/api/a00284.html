<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Matrix types with precision qualifiers</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">Matrix types with precision qualifiers<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix types with precision qualifiers which may result in various precision in term of ULPs.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga369b447bb1b312449b679ea1f90f3cea"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga369b447bb1b312449b679ea1f90f3cea">highp_dmat2</a></td></tr>
<tr class="memdesc:ga369b447bb1b312449b679ea1f90f3cea"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga369b447bb1b312449b679ea1f90f3cea">More...</a><br /></td></tr>
<tr class="separator:ga369b447bb1b312449b679ea1f90f3cea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae27ac20302c2e39b6c78e7fe18e62ef7"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gae27ac20302c2e39b6c78e7fe18e62ef7">highp_dmat2x2</a></td></tr>
<tr class="memdesc:gae27ac20302c2e39b6c78e7fe18e62ef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gae27ac20302c2e39b6c78e7fe18e62ef7">More...</a><br /></td></tr>
<tr class="separator:gae27ac20302c2e39b6c78e7fe18e62ef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4689ec33bc2c26e10132b174b49001a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gad4689ec33bc2c26e10132b174b49001a">highp_dmat2x3</a></td></tr>
<tr class="memdesc:gad4689ec33bc2c26e10132b174b49001a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gad4689ec33bc2c26e10132b174b49001a">More...</a><br /></td></tr>
<tr class="separator:gad4689ec33bc2c26e10132b174b49001a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ceeb46670fdc000a0701910cc5061c9"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga5ceeb46670fdc000a0701910cc5061c9">highp_dmat2x4</a></td></tr>
<tr class="memdesc:ga5ceeb46670fdc000a0701910cc5061c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga5ceeb46670fdc000a0701910cc5061c9">More...</a><br /></td></tr>
<tr class="separator:ga5ceeb46670fdc000a0701910cc5061c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86d6d4dbad92ffdcc759773340e15a97"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga86d6d4dbad92ffdcc759773340e15a97">highp_dmat3</a></td></tr>
<tr class="memdesc:ga86d6d4dbad92ffdcc759773340e15a97"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga86d6d4dbad92ffdcc759773340e15a97">More...</a><br /></td></tr>
<tr class="separator:ga86d6d4dbad92ffdcc759773340e15a97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3647309010a2160e9ec89bc6f7c95c35"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga3647309010a2160e9ec89bc6f7c95c35">highp_dmat3x2</a></td></tr>
<tr class="memdesc:ga3647309010a2160e9ec89bc6f7c95c35"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga3647309010a2160e9ec89bc6f7c95c35">More...</a><br /></td></tr>
<tr class="separator:ga3647309010a2160e9ec89bc6f7c95c35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae367ea93c4ad8a7c101dd27b8b2b04ce"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gae367ea93c4ad8a7c101dd27b8b2b04ce">highp_dmat3x3</a></td></tr>
<tr class="memdesc:gae367ea93c4ad8a7c101dd27b8b2b04ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gae367ea93c4ad8a7c101dd27b8b2b04ce">More...</a><br /></td></tr>
<tr class="separator:gae367ea93c4ad8a7c101dd27b8b2b04ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6543eeeb64f48d79a0b96484308c50f0"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga6543eeeb64f48d79a0b96484308c50f0">highp_dmat3x4</a></td></tr>
<tr class="memdesc:ga6543eeeb64f48d79a0b96484308c50f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga6543eeeb64f48d79a0b96484308c50f0">More...</a><br /></td></tr>
<tr class="separator:ga6543eeeb64f48d79a0b96484308c50f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga945254f459860741138bceb74da496b9"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga945254f459860741138bceb74da496b9">highp_dmat4</a></td></tr>
<tr class="memdesc:ga945254f459860741138bceb74da496b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga945254f459860741138bceb74da496b9">More...</a><br /></td></tr>
<tr class="separator:ga945254f459860741138bceb74da496b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeda1f474c668eaecc443bea85a4a4eca"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gaeda1f474c668eaecc443bea85a4a4eca">highp_dmat4x2</a></td></tr>
<tr class="memdesc:gaeda1f474c668eaecc443bea85a4a4eca"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gaeda1f474c668eaecc443bea85a4a4eca">More...</a><br /></td></tr>
<tr class="separator:gaeda1f474c668eaecc443bea85a4a4eca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacf237c2d8832fe8db2d7e187585d34bd"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gacf237c2d8832fe8db2d7e187585d34bd">highp_dmat4x3</a></td></tr>
<tr class="memdesc:gacf237c2d8832fe8db2d7e187585d34bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gacf237c2d8832fe8db2d7e187585d34bd">More...</a><br /></td></tr>
<tr class="separator:gacf237c2d8832fe8db2d7e187585d34bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga118d24a3d12c034e7cccef7bf2f01b8a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga118d24a3d12c034e7cccef7bf2f01b8a">highp_dmat4x4</a></td></tr>
<tr class="memdesc:ga118d24a3d12c034e7cccef7bf2f01b8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga118d24a3d12c034e7cccef7bf2f01b8a">More...</a><br /></td></tr>
<tr class="separator:ga118d24a3d12c034e7cccef7bf2f01b8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d5a0055544a516237dcdace049b143d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga4d5a0055544a516237dcdace049b143d">highp_mat2</a></td></tr>
<tr class="memdesc:ga4d5a0055544a516237dcdace049b143d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga4d5a0055544a516237dcdace049b143d">More...</a><br /></td></tr>
<tr class="separator:ga4d5a0055544a516237dcdace049b143d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2352ae43b284c9f71446674c0208c05d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga2352ae43b284c9f71446674c0208c05d">highp_mat2x2</a></td></tr>
<tr class="memdesc:ga2352ae43b284c9f71446674c0208c05d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga2352ae43b284c9f71446674c0208c05d">More...</a><br /></td></tr>
<tr class="separator:ga2352ae43b284c9f71446674c0208c05d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a0e3fe41512b0494e598f5c58722f19"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga7a0e3fe41512b0494e598f5c58722f19">highp_mat2x3</a></td></tr>
<tr class="memdesc:ga7a0e3fe41512b0494e598f5c58722f19"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga7a0e3fe41512b0494e598f5c58722f19">More...</a><br /></td></tr>
<tr class="separator:ga7a0e3fe41512b0494e598f5c58722f19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61f36a81f2ed1b5f9fc8bc3b26faec8f"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga61f36a81f2ed1b5f9fc8bc3b26faec8f">highp_mat2x4</a></td></tr>
<tr class="memdesc:ga61f36a81f2ed1b5f9fc8bc3b26faec8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga61f36a81f2ed1b5f9fc8bc3b26faec8f">More...</a><br /></td></tr>
<tr class="separator:ga61f36a81f2ed1b5f9fc8bc3b26faec8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3fd9849f3da5ed6e3decc3fb10a20b3e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga3fd9849f3da5ed6e3decc3fb10a20b3e">highp_mat3</a></td></tr>
<tr class="memdesc:ga3fd9849f3da5ed6e3decc3fb10a20b3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga3fd9849f3da5ed6e3decc3fb10a20b3e">More...</a><br /></td></tr>
<tr class="separator:ga3fd9849f3da5ed6e3decc3fb10a20b3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1eda47a00027ec440eac05d63739c71b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga1eda47a00027ec440eac05d63739c71b">highp_mat3x2</a></td></tr>
<tr class="memdesc:ga1eda47a00027ec440eac05d63739c71b"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga1eda47a00027ec440eac05d63739c71b">More...</a><br /></td></tr>
<tr class="separator:ga1eda47a00027ec440eac05d63739c71b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ea82e12f4d7afcfce8f59894d400230"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga2ea82e12f4d7afcfce8f59894d400230">highp_mat3x3</a></td></tr>
<tr class="memdesc:ga2ea82e12f4d7afcfce8f59894d400230"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga2ea82e12f4d7afcfce8f59894d400230">More...</a><br /></td></tr>
<tr class="separator:ga2ea82e12f4d7afcfce8f59894d400230"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6454b3a26ea30f69de8e44c08a63d1b7"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga6454b3a26ea30f69de8e44c08a63d1b7">highp_mat3x4</a></td></tr>
<tr class="memdesc:ga6454b3a26ea30f69de8e44c08a63d1b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga6454b3a26ea30f69de8e44c08a63d1b7">More...</a><br /></td></tr>
<tr class="separator:ga6454b3a26ea30f69de8e44c08a63d1b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad72e13d669d039f12ae5afa23148adc1"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gad72e13d669d039f12ae5afa23148adc1">highp_mat4</a></td></tr>
<tr class="memdesc:gad72e13d669d039f12ae5afa23148adc1"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#gad72e13d669d039f12ae5afa23148adc1">More...</a><br /></td></tr>
<tr class="separator:gad72e13d669d039f12ae5afa23148adc1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab68b66e6d2c37b804d0baf970fa4f0e5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gab68b66e6d2c37b804d0baf970fa4f0e5">highp_mat4x2</a></td></tr>
<tr class="memdesc:gab68b66e6d2c37b804d0baf970fa4f0e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#gab68b66e6d2c37b804d0baf970fa4f0e5">More...</a><br /></td></tr>
<tr class="separator:gab68b66e6d2c37b804d0baf970fa4f0e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d5a4e65fb976e4553b84995b95ecb38"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga8d5a4e65fb976e4553b84995b95ecb38">highp_mat4x3</a></td></tr>
<tr class="memdesc:ga8d5a4e65fb976e4553b84995b95ecb38"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga8d5a4e65fb976e4553b84995b95ecb38">More...</a><br /></td></tr>
<tr class="separator:ga8d5a4e65fb976e4553b84995b95ecb38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga58cc504be0e3b61c48bc91554a767b9f"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga58cc504be0e3b61c48bc91554a767b9f">highp_mat4x4</a></td></tr>
<tr class="memdesc:ga58cc504be0e3b61c48bc91554a767b9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs.  <a href="a00284.html#ga58cc504be0e3b61c48bc91554a767b9f">More...</a><br /></td></tr>
<tr class="separator:ga58cc504be0e3b61c48bc91554a767b9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8e2727a6e7aa68280245bb0022118e1"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gad8e2727a6e7aa68280245bb0022118e1">lowp_dmat2</a></td></tr>
<tr class="memdesc:gad8e2727a6e7aa68280245bb0022118e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gad8e2727a6e7aa68280245bb0022118e1">More...</a><br /></td></tr>
<tr class="separator:gad8e2727a6e7aa68280245bb0022118e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac61b94f5d9775f83f321bac899322fe2"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gac61b94f5d9775f83f321bac899322fe2">lowp_dmat2x2</a></td></tr>
<tr class="memdesc:gac61b94f5d9775f83f321bac899322fe2"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gac61b94f5d9775f83f321bac899322fe2">More...</a><br /></td></tr>
<tr class="separator:gac61b94f5d9775f83f321bac899322fe2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6bf2f5bde7ad5b9c289f777b93094af"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gaf6bf2f5bde7ad5b9c289f777b93094af">lowp_dmat2x3</a></td></tr>
<tr class="memdesc:gaf6bf2f5bde7ad5b9c289f777b93094af"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gaf6bf2f5bde7ad5b9c289f777b93094af">More...</a><br /></td></tr>
<tr class="separator:gaf6bf2f5bde7ad5b9c289f777b93094af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga97507a31ecee8609887d0f23bbde92c7"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga97507a31ecee8609887d0f23bbde92c7">lowp_dmat2x4</a></td></tr>
<tr class="memdesc:ga97507a31ecee8609887d0f23bbde92c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga97507a31ecee8609887d0f23bbde92c7">More...</a><br /></td></tr>
<tr class="separator:ga97507a31ecee8609887d0f23bbde92c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0cab80beee64a5f8d2ae4e823983063a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga0cab80beee64a5f8d2ae4e823983063a">lowp_dmat3</a></td></tr>
<tr class="memdesc:ga0cab80beee64a5f8d2ae4e823983063a"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga0cab80beee64a5f8d2ae4e823983063a">More...</a><br /></td></tr>
<tr class="separator:ga0cab80beee64a5f8d2ae4e823983063a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1e0ea3fba496bc7c6f620d2590acb66b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga1e0ea3fba496bc7c6f620d2590acb66b">lowp_dmat3x2</a></td></tr>
<tr class="memdesc:ga1e0ea3fba496bc7c6f620d2590acb66b"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga1e0ea3fba496bc7c6f620d2590acb66b">More...</a><br /></td></tr>
<tr class="separator:ga1e0ea3fba496bc7c6f620d2590acb66b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac017848a9df570f60916a21a297b1e8e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gac017848a9df570f60916a21a297b1e8e">lowp_dmat3x3</a></td></tr>
<tr class="memdesc:gac017848a9df570f60916a21a297b1e8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gac017848a9df570f60916a21a297b1e8e">More...</a><br /></td></tr>
<tr class="separator:gac017848a9df570f60916a21a297b1e8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga93add35d2a44c5830978b827e8c295e8"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga93add35d2a44c5830978b827e8c295e8">lowp_dmat3x4</a></td></tr>
<tr class="memdesc:ga93add35d2a44c5830978b827e8c295e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga93add35d2a44c5830978b827e8c295e8">More...</a><br /></td></tr>
<tr class="separator:ga93add35d2a44c5830978b827e8c295e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga708bc5b91bbfedd21debac8dcf2a64cd"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga708bc5b91bbfedd21debac8dcf2a64cd">lowp_dmat4</a></td></tr>
<tr class="memdesc:ga708bc5b91bbfedd21debac8dcf2a64cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga708bc5b91bbfedd21debac8dcf2a64cd">More...</a><br /></td></tr>
<tr class="separator:ga708bc5b91bbfedd21debac8dcf2a64cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga382dc5295cead78766239a8457abfa98"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga382dc5295cead78766239a8457abfa98">lowp_dmat4x2</a></td></tr>
<tr class="memdesc:ga382dc5295cead78766239a8457abfa98"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga382dc5295cead78766239a8457abfa98">More...</a><br /></td></tr>
<tr class="separator:ga382dc5295cead78766239a8457abfa98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d7ea07da7c6e5c81a3f4c8b3d44056e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga3d7ea07da7c6e5c81a3f4c8b3d44056e">lowp_dmat4x3</a></td></tr>
<tr class="memdesc:ga3d7ea07da7c6e5c81a3f4c8b3d44056e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga3d7ea07da7c6e5c81a3f4c8b3d44056e">More...</a><br /></td></tr>
<tr class="separator:ga3d7ea07da7c6e5c81a3f4c8b3d44056e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b0413198b7e9f061f7534a221c9dac9"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga5b0413198b7e9f061f7534a221c9dac9">lowp_dmat4x4</a></td></tr>
<tr class="memdesc:ga5b0413198b7e9f061f7534a221c9dac9"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga5b0413198b7e9f061f7534a221c9dac9">More...</a><br /></td></tr>
<tr class="separator:ga5b0413198b7e9f061f7534a221c9dac9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae400c4ce1f5f3e1fa12861b2baed331a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gae400c4ce1f5f3e1fa12861b2baed331a">lowp_mat2</a></td></tr>
<tr class="memdesc:gae400c4ce1f5f3e1fa12861b2baed331a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gae400c4ce1f5f3e1fa12861b2baed331a">More...</a><br /></td></tr>
<tr class="separator:gae400c4ce1f5f3e1fa12861b2baed331a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2df7cdaf9a571ce7a1b09435f502c694"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga2df7cdaf9a571ce7a1b09435f502c694">lowp_mat2x2</a></td></tr>
<tr class="memdesc:ga2df7cdaf9a571ce7a1b09435f502c694"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga2df7cdaf9a571ce7a1b09435f502c694">More...</a><br /></td></tr>
<tr class="separator:ga2df7cdaf9a571ce7a1b09435f502c694"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3eee3a74d0f1de8635d846dfb29ec4bb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga3eee3a74d0f1de8635d846dfb29ec4bb">lowp_mat2x3</a></td></tr>
<tr class="memdesc:ga3eee3a74d0f1de8635d846dfb29ec4bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga3eee3a74d0f1de8635d846dfb29ec4bb">More...</a><br /></td></tr>
<tr class="separator:ga3eee3a74d0f1de8635d846dfb29ec4bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade27f8324a16626cbce5d3e7da66b070"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gade27f8324a16626cbce5d3e7da66b070">lowp_mat2x4</a></td></tr>
<tr class="memdesc:gade27f8324a16626cbce5d3e7da66b070"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gade27f8324a16626cbce5d3e7da66b070">More...</a><br /></td></tr>
<tr class="separator:gade27f8324a16626cbce5d3e7da66b070"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6271ebc85ed778ccc15458c3d86fc854"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga6271ebc85ed778ccc15458c3d86fc854">lowp_mat3</a></td></tr>
<tr class="memdesc:ga6271ebc85ed778ccc15458c3d86fc854"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga6271ebc85ed778ccc15458c3d86fc854">More...</a><br /></td></tr>
<tr class="separator:ga6271ebc85ed778ccc15458c3d86fc854"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabf6cf90fd31efe25c94965507e98390"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gaabf6cf90fd31efe25c94965507e98390">lowp_mat3x2</a></td></tr>
<tr class="memdesc:gaabf6cf90fd31efe25c94965507e98390"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gaabf6cf90fd31efe25c94965507e98390">More...</a><br /></td></tr>
<tr class="separator:gaabf6cf90fd31efe25c94965507e98390"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga63362cb4a63fc1be7d2e49cd5d574c84"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga63362cb4a63fc1be7d2e49cd5d574c84">lowp_mat3x3</a></td></tr>
<tr class="memdesc:ga63362cb4a63fc1be7d2e49cd5d574c84"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga63362cb4a63fc1be7d2e49cd5d574c84">More...</a><br /></td></tr>
<tr class="separator:ga63362cb4a63fc1be7d2e49cd5d574c84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac5fc6786688eff02904ca5e7d6960092"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gac5fc6786688eff02904ca5e7d6960092">lowp_mat3x4</a></td></tr>
<tr class="memdesc:gac5fc6786688eff02904ca5e7d6960092"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gac5fc6786688eff02904ca5e7d6960092">More...</a><br /></td></tr>
<tr class="separator:gac5fc6786688eff02904ca5e7d6960092"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2dedee030500865267cd5851c00c139d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga2dedee030500865267cd5851c00c139d">lowp_mat4</a></td></tr>
<tr class="memdesc:ga2dedee030500865267cd5851c00c139d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga2dedee030500865267cd5851c00c139d">More...</a><br /></td></tr>
<tr class="separator:ga2dedee030500865267cd5851c00c139d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa3cdb8f24d09d761ec9ae2a4c7e5e21"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gafa3cdb8f24d09d761ec9ae2a4c7e5e21">lowp_mat4x2</a></td></tr>
<tr class="memdesc:gafa3cdb8f24d09d761ec9ae2a4c7e5e21"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#gafa3cdb8f24d09d761ec9ae2a4c7e5e21">More...</a><br /></td></tr>
<tr class="separator:gafa3cdb8f24d09d761ec9ae2a4c7e5e21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga534c3ef5c3b8fdd8656b6afc205b4b77"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga534c3ef5c3b8fdd8656b6afc205b4b77">lowp_mat4x3</a></td></tr>
<tr class="memdesc:ga534c3ef5c3b8fdd8656b6afc205b4b77"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga534c3ef5c3b8fdd8656b6afc205b4b77">More...</a><br /></td></tr>
<tr class="separator:ga534c3ef5c3b8fdd8656b6afc205b4b77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga686468a9a815bd4db8cddae42a6d6b87"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga686468a9a815bd4db8cddae42a6d6b87">lowp_mat4x4</a></td></tr>
<tr class="memdesc:ga686468a9a815bd4db8cddae42a6d6b87"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs.  <a href="a00284.html#ga686468a9a815bd4db8cddae42a6d6b87">More...</a><br /></td></tr>
<tr class="separator:ga686468a9a815bd4db8cddae42a6d6b87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6205fd19be355600334edef6af0b27cb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga6205fd19be355600334edef6af0b27cb">mediump_dmat2</a></td></tr>
<tr class="memdesc:ga6205fd19be355600334edef6af0b27cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga6205fd19be355600334edef6af0b27cb">More...</a><br /></td></tr>
<tr class="separator:ga6205fd19be355600334edef6af0b27cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga51dc36a7719cb458fa5114831c20d64f"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga51dc36a7719cb458fa5114831c20d64f">mediump_dmat2x2</a></td></tr>
<tr class="memdesc:ga51dc36a7719cb458fa5114831c20d64f"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga51dc36a7719cb458fa5114831c20d64f">More...</a><br /></td></tr>
<tr class="separator:ga51dc36a7719cb458fa5114831c20d64f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga741e05adf1f12d5d913f67088db1009a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga741e05adf1f12d5d913f67088db1009a">mediump_dmat2x3</a></td></tr>
<tr class="memdesc:ga741e05adf1f12d5d913f67088db1009a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga741e05adf1f12d5d913f67088db1009a">More...</a><br /></td></tr>
<tr class="separator:ga741e05adf1f12d5d913f67088db1009a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga685bda24922d112786af385deb4deb43"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga685bda24922d112786af385deb4deb43">mediump_dmat2x4</a></td></tr>
<tr class="memdesc:ga685bda24922d112786af385deb4deb43"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga685bda24922d112786af385deb4deb43">More...</a><br /></td></tr>
<tr class="separator:ga685bda24922d112786af385deb4deb43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga939fbf9c53008a8e84c7dd7cf8de29e2"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga939fbf9c53008a8e84c7dd7cf8de29e2">mediump_dmat3</a></td></tr>
<tr class="memdesc:ga939fbf9c53008a8e84c7dd7cf8de29e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga939fbf9c53008a8e84c7dd7cf8de29e2">More...</a><br /></td></tr>
<tr class="separator:ga939fbf9c53008a8e84c7dd7cf8de29e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2076157df85e49b8c021e03e46a376c1"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga2076157df85e49b8c021e03e46a376c1">mediump_dmat3x2</a></td></tr>
<tr class="memdesc:ga2076157df85e49b8c021e03e46a376c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga2076157df85e49b8c021e03e46a376c1">More...</a><br /></td></tr>
<tr class="separator:ga2076157df85e49b8c021e03e46a376c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga47bd2aae4701ee2fc865674a9df3d7a6"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga47bd2aae4701ee2fc865674a9df3d7a6">mediump_dmat3x3</a></td></tr>
<tr class="memdesc:ga47bd2aae4701ee2fc865674a9df3d7a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga47bd2aae4701ee2fc865674a9df3d7a6">More...</a><br /></td></tr>
<tr class="separator:ga47bd2aae4701ee2fc865674a9df3d7a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a132bd05675c2e46556f67cf738600b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga3a132bd05675c2e46556f67cf738600b">mediump_dmat3x4</a></td></tr>
<tr class="memdesc:ga3a132bd05675c2e46556f67cf738600b"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga3a132bd05675c2e46556f67cf738600b">More...</a><br /></td></tr>
<tr class="separator:ga3a132bd05675c2e46556f67cf738600b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf650bc667bf2a0e496b5a9182bc8d378"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gaf650bc667bf2a0e496b5a9182bc8d378">mediump_dmat4</a></td></tr>
<tr class="memdesc:gaf650bc667bf2a0e496b5a9182bc8d378"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gaf650bc667bf2a0e496b5a9182bc8d378">More...</a><br /></td></tr>
<tr class="separator:gaf650bc667bf2a0e496b5a9182bc8d378"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae220fa4c5a7b13ef2ab0420340de645c"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gae220fa4c5a7b13ef2ab0420340de645c">mediump_dmat4x2</a></td></tr>
<tr class="memdesc:gae220fa4c5a7b13ef2ab0420340de645c"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gae220fa4c5a7b13ef2ab0420340de645c">More...</a><br /></td></tr>
<tr class="separator:gae220fa4c5a7b13ef2ab0420340de645c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43ef60e4d996db15c9c8f069a96ff763"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga43ef60e4d996db15c9c8f069a96ff763">mediump_dmat4x3</a></td></tr>
<tr class="memdesc:ga43ef60e4d996db15c9c8f069a96ff763"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga43ef60e4d996db15c9c8f069a96ff763">More...</a><br /></td></tr>
<tr class="separator:ga43ef60e4d996db15c9c8f069a96ff763"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5389b3ab32dc0d72bea00057ab6d1dd3"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga5389b3ab32dc0d72bea00057ab6d1dd3">mediump_dmat4x4</a></td></tr>
<tr class="memdesc:ga5389b3ab32dc0d72bea00057ab6d1dd3"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga5389b3ab32dc0d72bea00057ab6d1dd3">More...</a><br /></td></tr>
<tr class="separator:ga5389b3ab32dc0d72bea00057ab6d1dd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga745452bd9c89f5ad948203e4fb4b4ea3"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga745452bd9c89f5ad948203e4fb4b4ea3">mediump_mat2</a></td></tr>
<tr class="memdesc:ga745452bd9c89f5ad948203e4fb4b4ea3"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga745452bd9c89f5ad948203e4fb4b4ea3">More...</a><br /></td></tr>
<tr class="separator:ga745452bd9c89f5ad948203e4fb4b4ea3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0cdf57d29f9448864237b2fb3e39aa1d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga0cdf57d29f9448864237b2fb3e39aa1d">mediump_mat2x2</a></td></tr>
<tr class="memdesc:ga0cdf57d29f9448864237b2fb3e39aa1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga0cdf57d29f9448864237b2fb3e39aa1d">More...</a><br /></td></tr>
<tr class="separator:ga0cdf57d29f9448864237b2fb3e39aa1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga497d513d552d927537d61fa11e3701ab"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga497d513d552d927537d61fa11e3701ab">mediump_mat2x3</a></td></tr>
<tr class="memdesc:ga497d513d552d927537d61fa11e3701ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga497d513d552d927537d61fa11e3701ab">More...</a><br /></td></tr>
<tr class="separator:ga497d513d552d927537d61fa11e3701ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7b75ea2e09fa686a79bbe9b6ca68ee5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gae7b75ea2e09fa686a79bbe9b6ca68ee5">mediump_mat2x4</a></td></tr>
<tr class="memdesc:gae7b75ea2e09fa686a79bbe9b6ca68ee5"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gae7b75ea2e09fa686a79bbe9b6ca68ee5">More...</a><br /></td></tr>
<tr class="separator:gae7b75ea2e09fa686a79bbe9b6ca68ee5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5aae49834d02732942f44e61d7bce136"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga5aae49834d02732942f44e61d7bce136">mediump_mat3</a></td></tr>
<tr class="memdesc:ga5aae49834d02732942f44e61d7bce136"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga5aae49834d02732942f44e61d7bce136">More...</a><br /></td></tr>
<tr class="separator:ga5aae49834d02732942f44e61d7bce136"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e1c9ee65fef547bde793e69723e24eb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga9e1c9ee65fef547bde793e69723e24eb">mediump_mat3x2</a></td></tr>
<tr class="memdesc:ga9e1c9ee65fef547bde793e69723e24eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga9e1c9ee65fef547bde793e69723e24eb">More...</a><br /></td></tr>
<tr class="separator:ga9e1c9ee65fef547bde793e69723e24eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc0f2f4ad21c90b341881cf056f8650e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gabc0f2f4ad21c90b341881cf056f8650e">mediump_mat3x3</a></td></tr>
<tr class="memdesc:gabc0f2f4ad21c90b341881cf056f8650e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gabc0f2f4ad21c90b341881cf056f8650e">More...</a><br /></td></tr>
<tr class="separator:gabc0f2f4ad21c90b341881cf056f8650e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa669c6675c3405f76c0b14020d1c0d61"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gaa669c6675c3405f76c0b14020d1c0d61">mediump_mat3x4</a></td></tr>
<tr class="memdesc:gaa669c6675c3405f76c0b14020d1c0d61"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gaa669c6675c3405f76c0b14020d1c0d61">More...</a><br /></td></tr>
<tr class="separator:gaa669c6675c3405f76c0b14020d1c0d61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab8531bc3f269aa45835cd6e1972b7fc7"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gab8531bc3f269aa45835cd6e1972b7fc7">mediump_mat4</a></td></tr>
<tr class="memdesc:gab8531bc3f269aa45835cd6e1972b7fc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gab8531bc3f269aa45835cd6e1972b7fc7">More...</a><br /></td></tr>
<tr class="separator:gab8531bc3f269aa45835cd6e1972b7fc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad75706b70545412ba9ac27d5ee210f66"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#gad75706b70545412ba9ac27d5ee210f66">mediump_mat4x2</a></td></tr>
<tr class="memdesc:gad75706b70545412ba9ac27d5ee210f66"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#gad75706b70545412ba9ac27d5ee210f66">More...</a><br /></td></tr>
<tr class="separator:gad75706b70545412ba9ac27d5ee210f66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a1440b5ea3cf84d5b06c79b534bd770"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga4a1440b5ea3cf84d5b06c79b534bd770">mediump_mat4x3</a></td></tr>
<tr class="memdesc:ga4a1440b5ea3cf84d5b06c79b534bd770"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga4a1440b5ea3cf84d5b06c79b534bd770">More...</a><br /></td></tr>
<tr class="separator:ga4a1440b5ea3cf84d5b06c79b534bd770"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15bca2b70917d9752231160d9da74b01"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html#ga15bca2b70917d9752231160d9da74b01">mediump_mat4x4</a></td></tr>
<tr class="memdesc:ga15bca2b70917d9752231160d9da74b01"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs.  <a href="a00284.html#ga15bca2b70917d9752231160d9da74b01">More...</a><br /></td></tr>
<tr class="separator:ga15bca2b70917d9752231160d9da74b01"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Matrix types with precision qualifiers which may result in various precision in term of ULPs. </p>
<p>GLSL allows defining qualifiers for particular variables. With OpenGL's GLSL, these qualifiers have no effect; they are there for compatibility, with OpenGL ES's GLSL, these qualifiers do have an effect.</p>
<p>C++ has no language equivalent to qualifier qualifiers. So GLM provides the next-best thing: a number of typedefs that use a particular qualifier.</p>
<p>None of these types make any guarantees about the actual qualifier used. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga369b447bb1b312449b679ea1f90f3cea"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f64, highp &gt; highp_dmat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00064_source.html#l00028">28</a> of file <a class="el" href="a00064_source.html">matrix_double2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae27ac20302c2e39b6c78e7fe18e62ef7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, double, highp &gt; highp_dmat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00064_source.html#l00046">46</a> of file <a class="el" href="a00064_source.html">matrix_double2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad4689ec33bc2c26e10132b174b49001a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, double, highp &gt; highp_dmat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00066_source.html#l00028">28</a> of file <a class="el" href="a00066_source.html">matrix_double2x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5ceeb46670fdc000a0701910cc5061c9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, double, highp &gt; highp_dmat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00068_source.html#l00028">28</a> of file <a class="el" href="a00068_source.html">matrix_double2x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga86d6d4dbad92ffdcc759773340e15a97"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f64, highp &gt; highp_dmat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00072_source.html#l00028">28</a> of file <a class="el" href="a00072_source.html">matrix_double3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3647309010a2160e9ec89bc6f7c95c35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, double, highp &gt; highp_dmat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00070_source.html#l00028">28</a> of file <a class="el" href="a00070_source.html">matrix_double3x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae367ea93c4ad8a7c101dd27b8b2b04ce"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, double, highp &gt; highp_dmat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00072_source.html#l00046">46</a> of file <a class="el" href="a00072_source.html">matrix_double3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6543eeeb64f48d79a0b96484308c50f0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, double, highp &gt; highp_dmat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00074_source.html#l00028">28</a> of file <a class="el" href="a00074_source.html">matrix_double3x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga945254f459860741138bceb74da496b9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f64, highp &gt; highp_dmat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00080_source.html#l00028">28</a> of file <a class="el" href="a00080_source.html">matrix_double4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaeda1f474c668eaecc443bea85a4a4eca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, double, highp &gt; highp_dmat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00076_source.html#l00028">28</a> of file <a class="el" href="a00076_source.html">matrix_double4x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gacf237c2d8832fe8db2d7e187585d34bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, double, highp &gt; highp_dmat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00078_source.html#l00028">28</a> of file <a class="el" href="a00078_source.html">matrix_double4x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga118d24a3d12c034e7cccef7bf2f01b8a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, double, highp &gt; highp_dmat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00080_source.html#l00046">46</a> of file <a class="el" href="a00080_source.html">matrix_double4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4d5a0055544a516237dcdace049b143d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, highp &gt; highp_mat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00083_source.html#l00028">28</a> of file <a class="el" href="a00083_source.html">matrix_float2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2352ae43b284c9f71446674c0208c05d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, highp &gt; highp_mat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00083_source.html#l00046">46</a> of file <a class="el" href="a00083_source.html">matrix_float2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7a0e3fe41512b0494e598f5c58722f19"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, f32, highp &gt; highp_mat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00085_source.html#l00028">28</a> of file <a class="el" href="a00085_source.html">matrix_float2x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga61f36a81f2ed1b5f9fc8bc3b26faec8f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, f32, highp &gt; highp_mat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00087_source.html#l00028">28</a> of file <a class="el" href="a00087_source.html">matrix_float2x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3fd9849f3da5ed6e3decc3fb10a20b3e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, highp &gt; highp_mat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00091_source.html#l00028">28</a> of file <a class="el" href="a00091_source.html">matrix_float3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1eda47a00027ec440eac05d63739c71b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, f32, highp &gt; highp_mat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00089_source.html#l00028">28</a> of file <a class="el" href="a00089_source.html">matrix_float3x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2ea82e12f4d7afcfce8f59894d400230"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, highp &gt; highp_mat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00091_source.html#l00046">46</a> of file <a class="el" href="a00091_source.html">matrix_float3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6454b3a26ea30f69de8e44c08a63d1b7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, f32, highp &gt; highp_mat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00093_source.html#l00028">28</a> of file <a class="el" href="a00093_source.html">matrix_float3x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad72e13d669d039f12ae5afa23148adc1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, highp &gt; highp_mat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00099_source.html#l00028">28</a> of file <a class="el" href="a00099_source.html">matrix_float4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab68b66e6d2c37b804d0baf970fa4f0e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, f32, highp &gt; highp_mat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00095_source.html#l00028">28</a> of file <a class="el" href="a00095_source.html">matrix_float4x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8d5a4e65fb976e4553b84995b95ecb38"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, f32, highp &gt; highp_mat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00097_source.html#l00028">28</a> of file <a class="el" href="a00097_source.html">matrix_float4x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga58cc504be0e3b61c48bc91554a767b9f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, highp &gt; highp_mat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers using high precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00099_source.html#l00046">46</a> of file <a class="el" href="a00099_source.html">matrix_float4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad8e2727a6e7aa68280245bb0022118e1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f64, lowp &gt; lowp_dmat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00064_source.html#l00016">16</a> of file <a class="el" href="a00064_source.html">matrix_double2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac61b94f5d9775f83f321bac899322fe2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, double, lowp &gt; lowp_dmat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00064_source.html#l00034">34</a> of file <a class="el" href="a00064_source.html">matrix_double2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf6bf2f5bde7ad5b9c289f777b93094af"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, double, lowp &gt; lowp_dmat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00066_source.html#l00016">16</a> of file <a class="el" href="a00066_source.html">matrix_double2x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga97507a31ecee8609887d0f23bbde92c7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, double, lowp &gt; lowp_dmat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00068_source.html#l00016">16</a> of file <a class="el" href="a00068_source.html">matrix_double2x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0cab80beee64a5f8d2ae4e823983063a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f64, lowp &gt; lowp_dmat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00072_source.html#l00016">16</a> of file <a class="el" href="a00072_source.html">matrix_double3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1e0ea3fba496bc7c6f620d2590acb66b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, double, lowp &gt; lowp_dmat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00070_source.html#l00016">16</a> of file <a class="el" href="a00070_source.html">matrix_double3x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac017848a9df570f60916a21a297b1e8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, double, lowp &gt; lowp_dmat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00072_source.html#l00034">34</a> of file <a class="el" href="a00072_source.html">matrix_double3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga93add35d2a44c5830978b827e8c295e8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, double, lowp &gt; lowp_dmat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00074_source.html#l00016">16</a> of file <a class="el" href="a00074_source.html">matrix_double3x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga708bc5b91bbfedd21debac8dcf2a64cd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f64, lowp &gt; lowp_dmat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00080_source.html#l00016">16</a> of file <a class="el" href="a00080_source.html">matrix_double4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga382dc5295cead78766239a8457abfa98"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, double, lowp &gt; lowp_dmat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00076_source.html#l00016">16</a> of file <a class="el" href="a00076_source.html">matrix_double4x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3d7ea07da7c6e5c81a3f4c8b3d44056e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, double, lowp &gt; lowp_dmat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00078_source.html#l00016">16</a> of file <a class="el" href="a00078_source.html">matrix_double4x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5b0413198b7e9f061f7534a221c9dac9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, double, lowp &gt; lowp_dmat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00080_source.html#l00034">34</a> of file <a class="el" href="a00080_source.html">matrix_double4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae400c4ce1f5f3e1fa12861b2baed331a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, lowp &gt; lowp_mat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00083_source.html#l00016">16</a> of file <a class="el" href="a00083_source.html">matrix_float2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2df7cdaf9a571ce7a1b09435f502c694"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, lowp &gt; lowp_mat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00083_source.html#l00034">34</a> of file <a class="el" href="a00083_source.html">matrix_float2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3eee3a74d0f1de8635d846dfb29ec4bb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, f32, lowp &gt; lowp_mat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00085_source.html#l00016">16</a> of file <a class="el" href="a00085_source.html">matrix_float2x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gade27f8324a16626cbce5d3e7da66b070"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, f32, lowp &gt; lowp_mat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00087_source.html#l00016">16</a> of file <a class="el" href="a00087_source.html">matrix_float2x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6271ebc85ed778ccc15458c3d86fc854"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, lowp &gt; lowp_mat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00091_source.html#l00016">16</a> of file <a class="el" href="a00091_source.html">matrix_float3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaabf6cf90fd31efe25c94965507e98390"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, f32, lowp &gt; lowp_mat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00089_source.html#l00016">16</a> of file <a class="el" href="a00089_source.html">matrix_float3x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga63362cb4a63fc1be7d2e49cd5d574c84"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, lowp &gt; lowp_mat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00091_source.html#l00034">34</a> of file <a class="el" href="a00091_source.html">matrix_float3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac5fc6786688eff02904ca5e7d6960092"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, f32, lowp &gt; lowp_mat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00093_source.html#l00016">16</a> of file <a class="el" href="a00093_source.html">matrix_float3x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2dedee030500865267cd5851c00c139d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, lowp &gt; lowp_mat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00099_source.html#l00016">16</a> of file <a class="el" href="a00099_source.html">matrix_float4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gafa3cdb8f24d09d761ec9ae2a4c7e5e21"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, f32, lowp &gt; lowp_mat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00095_source.html#l00016">16</a> of file <a class="el" href="a00095_source.html">matrix_float4x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga534c3ef5c3b8fdd8656b6afc205b4b77"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, f32, lowp &gt; lowp_mat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00097_source.html#l00016">16</a> of file <a class="el" href="a00097_source.html">matrix_float4x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga686468a9a815bd4db8cddae42a6d6b87"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, lowp &gt; lowp_mat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers using low precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00099_source.html#l00034">34</a> of file <a class="el" href="a00099_source.html">matrix_float4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6205fd19be355600334edef6af0b27cb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f64, mediump &gt; mediump_dmat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00064_source.html#l00022">22</a> of file <a class="el" href="a00064_source.html">matrix_double2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga51dc36a7719cb458fa5114831c20d64f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, double, mediump &gt; mediump_dmat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00064_source.html#l00040">40</a> of file <a class="el" href="a00064_source.html">matrix_double2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga741e05adf1f12d5d913f67088db1009a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, double, mediump &gt; mediump_dmat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00066_source.html#l00022">22</a> of file <a class="el" href="a00066_source.html">matrix_double2x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga685bda24922d112786af385deb4deb43"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, double, mediump &gt; mediump_dmat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00068_source.html#l00022">22</a> of file <a class="el" href="a00068_source.html">matrix_double2x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga939fbf9c53008a8e84c7dd7cf8de29e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f64, mediump &gt; mediump_dmat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00072_source.html#l00022">22</a> of file <a class="el" href="a00072_source.html">matrix_double3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2076157df85e49b8c021e03e46a376c1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, double, mediump &gt; mediump_dmat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00070_source.html#l00022">22</a> of file <a class="el" href="a00070_source.html">matrix_double3x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga47bd2aae4701ee2fc865674a9df3d7a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, double, mediump &gt; mediump_dmat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00072_source.html#l00040">40</a> of file <a class="el" href="a00072_source.html">matrix_double3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3a132bd05675c2e46556f67cf738600b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, double, mediump &gt; mediump_dmat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00074_source.html#l00022">22</a> of file <a class="el" href="a00074_source.html">matrix_double3x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf650bc667bf2a0e496b5a9182bc8d378"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f64, mediump &gt; mediump_dmat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00080_source.html#l00022">22</a> of file <a class="el" href="a00080_source.html">matrix_double4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae220fa4c5a7b13ef2ab0420340de645c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, double, mediump &gt; mediump_dmat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00076_source.html#l00022">22</a> of file <a class="el" href="a00076_source.html">matrix_double4x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga43ef60e4d996db15c9c8f069a96ff763"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, double, mediump &gt; mediump_dmat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00078_source.html#l00022">22</a> of file <a class="el" href="a00078_source.html">matrix_double4x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5389b3ab32dc0d72bea00057ab6d1dd3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, double, mediump &gt; mediump_dmat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00080_source.html#l00040">40</a> of file <a class="el" href="a00080_source.html">matrix_double4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga745452bd9c89f5ad948203e4fb4b4ea3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, mediump &gt; mediump_mat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00083_source.html#l00022">22</a> of file <a class="el" href="a00083_source.html">matrix_float2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0cdf57d29f9448864237b2fb3e39aa1d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, mediump &gt; mediump_mat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00083_source.html#l00040">40</a> of file <a class="el" href="a00083_source.html">matrix_float2x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga497d513d552d927537d61fa11e3701ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, f32, mediump &gt; mediump_mat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00085_source.html#l00022">22</a> of file <a class="el" href="a00085_source.html">matrix_float2x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae7b75ea2e09fa686a79bbe9b6ca68ee5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, f32, mediump &gt; mediump_mat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00087_source.html#l00022">22</a> of file <a class="el" href="a00087_source.html">matrix_float2x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5aae49834d02732942f44e61d7bce136"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, mediump &gt; mediump_mat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00091_source.html#l00022">22</a> of file <a class="el" href="a00091_source.html">matrix_float3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9e1c9ee65fef547bde793e69723e24eb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, f32, mediump &gt; mediump_mat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00089_source.html#l00022">22</a> of file <a class="el" href="a00089_source.html">matrix_float3x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gabc0f2f4ad21c90b341881cf056f8650e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, mediump &gt; mediump_mat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00091_source.html#l00040">40</a> of file <a class="el" href="a00091_source.html">matrix_float3x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa669c6675c3405f76c0b14020d1c0d61"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, f32, mediump &gt; mediump_mat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00093_source.html#l00022">22</a> of file <a class="el" href="a00093_source.html">matrix_float3x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab8531bc3f269aa45835cd6e1972b7fc7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, mediump &gt; mediump_mat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00099_source.html#l00022">22</a> of file <a class="el" href="a00099_source.html">matrix_float4x4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad75706b70545412ba9ac27d5ee210f66"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, f32, mediump &gt; mediump_mat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00095_source.html#l00022">22</a> of file <a class="el" href="a00095_source.html">matrix_float4x2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4a1440b5ea3cf84d5b06c79b534bd770"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, f32, mediump &gt; mediump_mat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00097_source.html#l00022">22</a> of file <a class="el" href="a00097_source.html">matrix_float4x3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga15bca2b70917d9752231160d9da74b01"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, mediump &gt; mediump_mat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00099_source.html#l00040">40</a> of file <a class="el" href="a00099_source.html">matrix_float4x4_precision.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
