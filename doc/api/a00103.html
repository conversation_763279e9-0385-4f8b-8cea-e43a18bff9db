<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_major_storage.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">matrix_major_storage.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00338.html">GLM_GTX_matrix_major_storage</a>  
<a href="#details">More...</a></p>

<p><a href="a00103_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaaff72f11286e59a4a88ed21a347f284c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaaff72f11286e59a4a88ed21a347f284c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gaaff72f11286e59a4a88ed21a347f284c">colMajor2</a> (vec&lt; 2, T, Q &gt; const &amp;v1, vec&lt; 2, T, Q &gt; const &amp;v2)</td></tr>
<tr class="memdesc:gaaff72f11286e59a4a88ed21a347f284c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a column major matrix from column vectors.  <a href="a00338.html#gaaff72f11286e59a4a88ed21a347f284c">More...</a><br /></td></tr>
<tr class="separator:gaaff72f11286e59a4a88ed21a347f284c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc25fd44196c92b1397b127aec1281ab"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafc25fd44196c92b1397b127aec1281ab"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gafc25fd44196c92b1397b127aec1281ab">colMajor2</a> (mat&lt; 2, 2, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gafc25fd44196c92b1397b127aec1281ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a column major matrix from other matrix.  <a href="a00338.html#gafc25fd44196c92b1397b127aec1281ab">More...</a><br /></td></tr>
<tr class="separator:gafc25fd44196c92b1397b127aec1281ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1e25b72b085087740c92f5c70f3b051f"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1e25b72b085087740c92f5c70f3b051f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#ga1e25b72b085087740c92f5c70f3b051f">colMajor3</a> (vec&lt; 3, T, Q &gt; const &amp;v1, vec&lt; 3, T, Q &gt; const &amp;v2, vec&lt; 3, T, Q &gt; const &amp;v3)</td></tr>
<tr class="memdesc:ga1e25b72b085087740c92f5c70f3b051f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a column major matrix from column vectors.  <a href="a00338.html#ga1e25b72b085087740c92f5c70f3b051f">More...</a><br /></td></tr>
<tr class="separator:ga1e25b72b085087740c92f5c70f3b051f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86bd0656e787bb7f217607572590af27"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga86bd0656e787bb7f217607572590af27"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#ga86bd0656e787bb7f217607572590af27">colMajor3</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:ga86bd0656e787bb7f217607572590af27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a column major matrix from other matrix.  <a href="a00338.html#ga86bd0656e787bb7f217607572590af27">More...</a><br /></td></tr>
<tr class="separator:ga86bd0656e787bb7f217607572590af27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4aa6c7e17bfce41a6c13bf6469fab05"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf4aa6c7e17bfce41a6c13bf6469fab05"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gaf4aa6c7e17bfce41a6c13bf6469fab05">colMajor4</a> (vec&lt; 4, T, Q &gt; const &amp;v1, vec&lt; 4, T, Q &gt; const &amp;v2, vec&lt; 4, T, Q &gt; const &amp;v3, vec&lt; 4, T, Q &gt; const &amp;v4)</td></tr>
<tr class="memdesc:gaf4aa6c7e17bfce41a6c13bf6469fab05"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a column major matrix from column vectors.  <a href="a00338.html#gaf4aa6c7e17bfce41a6c13bf6469fab05">More...</a><br /></td></tr>
<tr class="separator:gaf4aa6c7e17bfce41a6c13bf6469fab05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf3f9511c366c20ba2e4a64c9e4cec2b3"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf3f9511c366c20ba2e4a64c9e4cec2b3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gaf3f9511c366c20ba2e4a64c9e4cec2b3">colMajor4</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gaf3f9511c366c20ba2e4a64c9e4cec2b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a column major matrix from other matrix.  <a href="a00338.html#gaf3f9511c366c20ba2e4a64c9e4cec2b3">More...</a><br /></td></tr>
<tr class="separator:gaf3f9511c366c20ba2e4a64c9e4cec2b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8">rowMajor2</a> (vec&lt; 2, T, Q &gt; const &amp;v1, vec&lt; 2, T, Q &gt; const &amp;v2)</td></tr>
<tr class="memdesc:gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a row major matrix from row vectors.  <a href="a00338.html#gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8">More...</a><br /></td></tr>
<tr class="separator:gaf5b1aee9e3eb1acf9d6c3c8be1e73bb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf66c75ed69ca9e87462550708c2c6726"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf66c75ed69ca9e87462550708c2c6726"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gaf66c75ed69ca9e87462550708c2c6726">rowMajor2</a> (mat&lt; 2, 2, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gaf66c75ed69ca9e87462550708c2c6726"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a row major matrix from other matrix.  <a href="a00338.html#gaf66c75ed69ca9e87462550708c2c6726">More...</a><br /></td></tr>
<tr class="separator:gaf66c75ed69ca9e87462550708c2c6726"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ae46497493339f745754e40f438442e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2ae46497493339f745754e40f438442e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#ga2ae46497493339f745754e40f438442e">rowMajor3</a> (vec&lt; 3, T, Q &gt; const &amp;v1, vec&lt; 3, T, Q &gt; const &amp;v2, vec&lt; 3, T, Q &gt; const &amp;v3)</td></tr>
<tr class="memdesc:ga2ae46497493339f745754e40f438442e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a row major matrix from row vectors.  <a href="a00338.html#ga2ae46497493339f745754e40f438442e">More...</a><br /></td></tr>
<tr class="separator:ga2ae46497493339f745754e40f438442e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8a3a50ab47bbe8d36cdb81d90dfcf77"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad8a3a50ab47bbe8d36cdb81d90dfcf77"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gad8a3a50ab47bbe8d36cdb81d90dfcf77">rowMajor3</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gad8a3a50ab47bbe8d36cdb81d90dfcf77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a row major matrix from other matrix.  <a href="a00338.html#gad8a3a50ab47bbe8d36cdb81d90dfcf77">More...</a><br /></td></tr>
<tr class="separator:gad8a3a50ab47bbe8d36cdb81d90dfcf77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9636cd6bbe2c32a8d0c03ffb8b1ef284"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga9636cd6bbe2c32a8d0c03ffb8b1ef284"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#ga9636cd6bbe2c32a8d0c03ffb8b1ef284">rowMajor4</a> (vec&lt; 4, T, Q &gt; const &amp;v1, vec&lt; 4, T, Q &gt; const &amp;v2, vec&lt; 4, T, Q &gt; const &amp;v3, vec&lt; 4, T, Q &gt; const &amp;v4)</td></tr>
<tr class="memdesc:ga9636cd6bbe2c32a8d0c03ffb8b1ef284"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a row major matrix from row vectors.  <a href="a00338.html#ga9636cd6bbe2c32a8d0c03ffb8b1ef284">More...</a><br /></td></tr>
<tr class="separator:ga9636cd6bbe2c32a8d0c03ffb8b1ef284"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac92ad1c2acdf18d3eb7be45a32f9566b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac92ad1c2acdf18d3eb7be45a32f9566b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00338.html#gac92ad1c2acdf18d3eb7be45a32f9566b">rowMajor4</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gac92ad1c2acdf18d3eb7be45a32f9566b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a row major matrix from other matrix.  <a href="a00338.html#gac92ad1c2acdf18d3eb7be45a32f9566b">More...</a><br /></td></tr>
<tr class="separator:gac92ad1c2acdf18d3eb7be45a32f9566b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00338.html">GLM_GTX_matrix_major_storage</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
gtx_extented_min_max (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00103_source.html">matrix_major_storage.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
