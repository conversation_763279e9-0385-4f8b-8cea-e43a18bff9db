<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: _swizzle.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_033f5edb0915b828d2c46ed4804e5503.html">detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">_swizzle.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;</div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;{</div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;        <span class="comment">// Internal class for implementing swizzle operators</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;        <span class="keyword">struct </span>_swizzle_base0</div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;        {</div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;        <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;                GLM_FUNC_QUALIFIER T&amp; elem(<span class="keywordtype">size_t</span> i){ <span class="keywordflow">return</span> (reinterpret_cast&lt;T*&gt;(_buffer))[i]; }</div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;                GLM_FUNC_QUALIFIER T <span class="keyword">const</span>&amp; elem(<span class="keywordtype">size_t</span> i)<span class="keyword"> const</span>{ <span class="keywordflow">return</span> (reinterpret_cast&lt;const T*&gt;(_buffer))[i]; }</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;                <span class="comment">// Use an opaque buffer to *ensure* the compiler doesn&#39;t call a constructor.</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;                <span class="comment">// The size 1 buffer is assumed to aligned to the actual members so that the</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;                <span class="comment">// elem()</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;                <span class="keywordtype">char</span>    _buffer[1];</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;        };</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> N, <span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2, <span class="keywordtype">int</span> E3, <span class="keywordtype">bool</span> Aligned&gt;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;        <span class="keyword">struct </span>_swizzle_base1 : <span class="keyword">public</span> _swizzle_base0&lt;T, N&gt;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;        {</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;        };</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">bool</span> Aligned&gt;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;        <span class="keyword">struct </span>_swizzle_base1&lt;2, T, Q, E0,E1,-1,-2, Aligned&gt; : <span class="keyword">public</span> _swizzle_base0&lt;T, 2&gt;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;        {</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;                GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; operator ()()<span class="keyword">  const </span>{ <span class="keywordflow">return</span> vec&lt;2, T, Q&gt;(this-&gt;elem(E0), this-&gt;elem(E1)); }</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;        };</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2, <span class="keywordtype">bool</span> Aligned&gt;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        <span class="keyword">struct </span>_swizzle_base1&lt;3, T, Q, E0,E1,E2,-1, Aligned&gt; : <span class="keyword">public</span> _swizzle_base0&lt;T, 3&gt;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        {</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; operator ()()<span class="keyword">  const </span>{ <span class="keywordflow">return</span> vec&lt;3, T, Q&gt;(this-&gt;elem(E0), this-&gt;elem(E1), this-&gt;elem(E2)); }</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        };</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2, <span class="keywordtype">int</span> E3, <span class="keywordtype">bool</span> Aligned&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">struct </span>_swizzle_base1&lt;4, T, Q, E0,E1,E2,E3, Aligned&gt; : <span class="keyword">public</span> _swizzle_base0&lt;T, 4&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        {</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; operator ()()<span class="keyword">  const </span>{ <span class="keywordflow">return</span> vec&lt;4, T, Q&gt;(this-&gt;elem(E0), this-&gt;elem(E1), this-&gt;elem(E2), this-&gt;elem(E3)); }</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        };</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="comment">// Internal class for implementing swizzle operators</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="comment">                Template parameters:</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="comment"></span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="comment">                T                       = type of scalar values (e.g. float, double)</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="comment">                N                       = number of components in the vector (e.g. 3)</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="comment">                E0...3          = what index the n-th element of this swizzle refers to in the unswizzled vec</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="comment"></span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="comment">                DUPLICATE_ELEMENTS = 1 if there is a repeated element, 0 otherwise (used to specialize swizzles</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="comment">                        containing duplicate elements so that they cannot be used as r-values).</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="comment">        */</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> N, <span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2, <span class="keywordtype">int</span> E3, <span class="keywordtype">int</span> DUPLICATE_ELEMENTS&gt;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keyword">struct </span>_swizzle_base2 : <span class="keyword">public</span> _swizzle_base1&lt;N, T, Q, E0,E1,E2,E3, detail::is_aligned&lt;Q&gt;::value&gt;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        {</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;                <span class="keyword">struct </span>op_equal</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                {</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                        GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator() (T&amp; <a class="code" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a>, T&amp; t)<span class="keyword"> const</span>{ e = t; }</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                };</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                <span class="keyword">struct </span>op_minus</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                {</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                        GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator() (T&amp; <a class="code" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a>, T&amp; t)<span class="keyword"> const</span>{ e -= t; }</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                };</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                <span class="keyword">struct </span>op_plus</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                {</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                        GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator() (T&amp; <a class="code" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a>, T&amp; t)<span class="keyword"> const</span>{ e += t; }</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                };</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                <span class="keyword">struct </span>op_mul</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                {</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                        GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator() (T&amp; <a class="code" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a>, T&amp; t)<span class="keyword"> const</span>{ e *= t; }</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                };</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                <span class="keyword">struct </span>op_div</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                {</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                        GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator() (T&amp; <a class="code" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a>, T&amp; t)<span class="keyword"> const</span>{ e /= t; }</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                };</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                GLM_FUNC_QUALIFIER _swizzle_base2&amp; operator= (<span class="keyword">const</span> T&amp; t)</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                {</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i)</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;                                (*<span class="keyword">this</span>)[i] = t;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;                        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                }</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                GLM_FUNC_QUALIFIER _swizzle_base2&amp; operator= (vec&lt;N, T, Q&gt; <span class="keyword">const</span>&amp; that)</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                {</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;                        _apply_op(that, op_equal());</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator -= (vec&lt;N, T, Q&gt; <span class="keyword">const</span>&amp; that)</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                {</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                        _apply_op(that, op_minus());</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                }</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator += (vec&lt;N, T, Q&gt; <span class="keyword">const</span>&amp; that)</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;                {</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                        _apply_op(that, op_plus());</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                }</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator *= (vec&lt;N, T, Q&gt; <span class="keyword">const</span>&amp; that)</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                {</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                        _apply_op(that, op_mul());</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                }</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> operator /= (vec&lt;N, T, Q&gt; <span class="keyword">const</span>&amp; that)</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;                {</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;                        _apply_op(that, op_div());</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                }</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                GLM_FUNC_QUALIFIER T&amp; operator[](<span class="keywordtype">size_t</span> i)</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;                {</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                        <span class="keyword">const</span> <span class="keywordtype">int</span> offset_dst[4] = { E0, E1, E2, E3 };</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                        <span class="keywordflow">return</span> this-&gt;elem(offset_dst[i]);</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                }</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                GLM_FUNC_QUALIFIER T operator[](<span class="keywordtype">size_t</span> i)<span class="keyword"> const</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                        <span class="keyword">const</span> <span class="keywordtype">int</span> offset_dst[4] = { E0, E1, E2, E3 };</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                        <span class="keywordflow">return</span> this-&gt;elem(offset_dst[i]);</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                }</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;                GLM_FUNC_QUALIFIER <span class="keywordtype">void</span> _apply_op(vec&lt;N, T, Q&gt; <span class="keyword">const</span>&amp; that, <span class="keyword">const</span> U&amp; op)</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;                {</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                        <span class="comment">// Make a copy of the data in this == &amp;that.</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;                        <span class="comment">// The copier should optimize out the copy in cases where the function is</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;                        <span class="comment">// properly inlined and the copy is not necessary.</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                        T t[N];</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;                        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i)</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;                                t[i] = that[i];</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i)</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;                                op( (*<span class="keyword">this</span>)[i], t[i] );</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;                }</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        };</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        <span class="comment">// Specialization for swizzles containing duplicate elements.  These cannot be modified.</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> N, <span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2, <span class="keywordtype">int</span> E3&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <span class="keyword">struct </span>_swizzle_base2&lt;N, T, Q, E0,E1,E2,E3, 1&gt; : <span class="keyword">public</span> _swizzle_base1&lt;N, T, Q, E0,E1,E2,E3, detail::is_aligned&lt;Q&gt;::value&gt;</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        {</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;                <span class="keyword">struct </span>Stub {};</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;                GLM_FUNC_QUALIFIER _swizzle_base2&amp; operator= (Stub <span class="keyword">const</span>&amp;) { <span class="keywordflow">return</span> *<span class="keyword">this</span>; }</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;                GLM_FUNC_QUALIFIER T operator[]  (<span class="keywordtype">size_t</span> i)<span class="keyword"> const</span></div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;                        <span class="keyword">const</span> <span class="keywordtype">int</span> offset_dst[4] = { E0, E1, E2, E3 };</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;                        <span class="keywordflow">return</span> this-&gt;elem(offset_dst[i]);</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;                }</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        };</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> N, <span class="keyword">typename</span> T, qualifier Q, <span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2, <span class="keywordtype">int</span> E3&gt;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        <span class="keyword">struct </span>_swizzle : <span class="keyword">public</span> _swizzle_base2&lt;N, T, Q, E0, E1, E2, E3, (E0 == E1 || E0 == E2 || E0 == E3 || E1 == E2 || E1 == E3 || E2 == E3)&gt;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        {</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;                <span class="keyword">typedef</span> _swizzle_base2&lt;N, T, Q, E0, E1, E2, E3, (E0 == E1 || E0 == E2 || E0 == E3 || E1 == E2 || E1 == E3 || E2 == E3)&gt; base_type;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;                <span class="keyword">using</span> base_type::operator=;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;                GLM_FUNC_QUALIFIER <span class="keyword">operator</span> vec&lt;N, T, Q&gt; () <span class="keyword">const</span> { <span class="keywordflow">return</span> (*<span class="keyword">this</span>)(); }</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        };</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;<span class="comment">// To prevent the C++ syntax from getting entirely overwhelming, define some alias macros</span></div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_TEMPLATE1   template&lt;int N, typename T, qualifier Q, int E0, int E1, int E2, int E3&gt;</span></div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_TEMPLATE2   template&lt;int N, typename T, qualifier Q, int E0, int E1, int E2, int E3, int F0, int F1, int F2, int F3&gt;</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_TYPE1       _swizzle&lt;N, T, Q, E0, E1, E2, E3&gt;</span></div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_TYPE2       _swizzle&lt;N, T, Q, F0, F1, F2, F3&gt;</span></div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="comment">// Wrapper for a binary operator (e.g. u.yy + v.zy)</span></div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_VECTOR_BINARY_OPERATOR_IMPLEMENTATION(OPERAND)                 \</span></div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE2                                                          \</span></div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER vec&lt;N, T, Q&gt; operator OPERAND ( const GLM_SWIZZLE_TYPE1&amp; a, const GLM_SWIZZLE_TYPE2&amp; b)  \</span></div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;<span class="preprocessor">        {                                                                               \</span></div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;<span class="preprocessor">                return a() OPERAND b();                                                     \</span></div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="preprocessor">        }                                                                               \</span></div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                          \</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER vec&lt;N, T, Q&gt; operator OPERAND ( const GLM_SWIZZLE_TYPE1&amp; a, const vec&lt;N, T, Q&gt;&amp; b)                   \</span></div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="preprocessor">        {                                                                               \</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="preprocessor">                return a() OPERAND b;                                                       \</span></div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;<span class="preprocessor">        }                                                                               \</span></div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                          \</span></div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER vec&lt;N, T, Q&gt; operator OPERAND ( const vec&lt;N, T, Q&gt;&amp; a, const GLM_SWIZZLE_TYPE1&amp; b)                   \</span></div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<span class="preprocessor">        {                                                                               \</span></div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;<span class="preprocessor">                return a OPERAND b();                                                       \</span></div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;<span class="preprocessor">        }</span></div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="comment">// Wrapper for a operand between a swizzle and a binary (e.g. 1.0f - u.xyz)</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_SCALAR_BINARY_OPERATOR_IMPLEMENTATION(OPERAND)                                                              \</span></div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                                                                           \</span></div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER vec&lt;N, T, Q&gt; operator OPERAND ( const GLM_SWIZZLE_TYPE1&amp; a, const T&amp; b)      \</span></div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="preprocessor">        {                                                                                                                                                                                       \</span></div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="preprocessor">                return a() OPERAND b;                                                                                                                                   \</span></div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;<span class="preprocessor">        }                                                                                                                                                                                       \</span></div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                                                                           \</span></div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER vec&lt;N, T, Q&gt; operator OPERAND ( const T&amp; a, const GLM_SWIZZLE_TYPE1&amp; b)      \</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="preprocessor">        {                                                                                                                                                                                       \</span></div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;<span class="preprocessor">                return a OPERAND b();                                                                                                                                   \</span></div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;<span class="preprocessor">        }</span></div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;<span class="comment">// Macro for wrapping a function taking one argument (e.g. abs())</span></div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_FUNCTION_1_ARGS(RETURN_TYPE,FUNCTION)                                                                                               \</span></div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                                                                                           \</span></div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a)         \</span></div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;<span class="preprocessor">        {                                                                                                                                                                                                       \</span></div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;<span class="preprocessor">                return FUNCTION(a());                                                                                                                                                   \</span></div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="preprocessor">        }</span></div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;<span class="comment">// Macro for wrapping a function taking two vector arguments (e.g. dot()).</span></div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_FUNCTION_2_ARGS(RETURN_TYPE,FUNCTION)                                                       \</span></div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE2                                                                                       \</span></div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a, const GLM_SWIZZLE_TYPE2&amp; b) \</span></div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;<span class="preprocessor">        {                                                                                                           \</span></div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;<span class="preprocessor">                return FUNCTION(a(), b());                                                                              \</span></div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;<span class="preprocessor">        }                                                                                                           \</span></div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                       \</span></div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a, const GLM_SWIZZLE_TYPE1&amp; b) \</span></div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;<span class="preprocessor">        {                                                                                                           \</span></div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;<span class="preprocessor">                return FUNCTION(a(), b());                                                                              \</span></div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;<span class="preprocessor">        }                                                                                                           \</span></div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                       \</span></div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a, const typename V&amp; b)         \</span></div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;<span class="preprocessor">        {                                                                                                           \</span></div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;<span class="preprocessor">                return FUNCTION(a(), b);                                                                                \</span></div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;<span class="preprocessor">        }                                                                                                           \</span></div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                       \</span></div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const V&amp; a, const GLM_SWIZZLE_TYPE1&amp; b)                  \</span></div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;<span class="preprocessor">        {                                                                                                           \</span></div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;<span class="preprocessor">                return FUNCTION(a, b());                                                                                \</span></div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;<span class="preprocessor">        }</span></div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;<span class="comment">// Macro for wrapping a function take 2 vec arguments followed by a scalar (e.g. mix()).</span></div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_FUNCTION_2_ARGS_SCALAR(RETURN_TYPE,FUNCTION)                                                             \</span></div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE2                                                                                                    \</span></div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a, const GLM_SWIZZLE_TYPE2&amp; b, const T&amp; c)   \</span></div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="preprocessor">        {                                                                                                                         \</span></div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;<span class="preprocessor">                return FUNCTION(a(), b(), c);                                                                                         \</span></div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;<span class="preprocessor">        }                                                                                                                         \</span></div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                                    \</span></div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a, const GLM_SWIZZLE_TYPE1&amp; b, const T&amp; c)   \</span></div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;<span class="preprocessor">        {                                                                                                                         \</span></div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;<span class="preprocessor">                return FUNCTION(a(), b(), c);                                                                                         \</span></div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;<span class="preprocessor">        }                                                                                                                         \</span></div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                                    \</span></div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const GLM_SWIZZLE_TYPE1&amp; a, const typename S0::vec_type&amp; b, const T&amp; c)\</span></div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;<span class="preprocessor">        {                                                                                                                         \</span></div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;<span class="preprocessor">                return FUNCTION(a(), b, c);                                                                                           \</span></div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;<span class="preprocessor">        }                                                                                                                         \</span></div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;<span class="preprocessor">        GLM_SWIZZLE_TEMPLATE1                                                                                                    \</span></div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;<span class="preprocessor">        GLM_FUNC_QUALIFIER typename GLM_SWIZZLE_TYPE1::RETURN_TYPE FUNCTION(const typename V&amp; a, const GLM_SWIZZLE_TYPE1&amp; b, const T&amp; c)           \</span></div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;<span class="preprocessor">        {                                                                                                                         \</span></div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;<span class="preprocessor">                return FUNCTION(a, b(), c);                                                                                           \</span></div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;<span class="preprocessor">        }</span></div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;{</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;        <span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;        {</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;                GLM_SWIZZLE_SCALAR_BINARY_OPERATOR_IMPLEMENTATION(-)</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;                GLM_SWIZZLE_SCALAR_BINARY_OPERATOR_IMPLEMENTATION(*)</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;                GLM_SWIZZLE_VECTOR_BINARY_OPERATOR_IMPLEMENTATION(+)</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;                GLM_SWIZZLE_VECTOR_BINARY_OPERATOR_IMPLEMENTATION(-)</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;                GLM_SWIZZLE_VECTOR_BINARY_OPERATOR_IMPLEMENTATION(*)</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;                GLM_SWIZZLE_VECTOR_BINARY_OPERATOR_IMPLEMENTATION(/)</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;        }</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;        <span class="comment">//</span></div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;        <span class="comment">// Swizzles are distinct types from the unswizzled type.  The below macros will</span></div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;        <span class="comment">// provide template specializations for the swizzle types for the given functions</span></div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;        <span class="comment">// so that the compiler does not have any ambiguity to choosing how to handle</span></div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;        <span class="comment">// the function.</span></div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        <span class="comment">//</span></div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;        <span class="comment">// The alternative is to use the operator()() when calling the function in order</span></div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;        <span class="comment">// to explicitly convert the swizzled type to the unswizzled type.</span></div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;        <span class="comment">//</span></div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_1_ARGS(vec_type,    abs);</span></div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_1_ARGS(vec_type,    acos);</span></div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_1_ARGS(vec_type,    acosh);</span></div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_1_ARGS(vec_type,    all);</span></div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_1_ARGS(vec_type,    any);</span></div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_2_ARGS(value_type,  dot);</span></div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_2_ARGS(vec_type,    cross);</span></div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_2_ARGS(vec_type,    step);</span></div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;        <span class="comment">//GLM_SWIZZLE_FUNCTION_2_ARGS_SCALAR(vec_type, mix);</span></div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;}</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE2_2_MEMBERS(T, Q, E0,E1) \</span></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2, T, Q, 0,0,-1,-2&gt; E0 ## E0; }; \</span></div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2, T, Q, 0,1,-1,-2&gt; E0 ## E1; }; \</span></div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2, T, Q, 1,0,-1,-2&gt; E1 ## E0; }; \</span></div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2, T, Q, 1,1,-1,-2&gt; E1 ## E1; };</span></div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE2_3_MEMBERS(T, Q, E0,E1) \</span></div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 0,0,0,-1&gt; E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 0,0,1,-1&gt; E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 0,1,0,-1&gt; E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 0,1,1,-1&gt; E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 1,0,0,-1&gt; E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 1,0,1,-1&gt; E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 1,1,0,-1&gt; E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3,T, Q, 1,1,1,-1&gt; E1 ## E1 ## E1; };</span></div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE2_4_MEMBERS(T, Q, E0,E1) \</span></div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,0,0&gt; E0 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,0,1&gt; E0 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,1,0&gt; E0 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,1,1&gt; E0 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,0,0&gt; E0 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,0,1&gt; E0 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,1,0&gt; E0 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,1,1&gt; E0 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,0,0&gt; E1 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,0,1&gt; E1 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,1,0&gt; E1 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,1,1&gt; E1 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,0,0&gt; E1 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,0,1&gt; E1 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,1,0&gt; E1 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,1,1&gt; E1 ## E1 ## E1 ## E1; };</span></div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE3_2_MEMBERS(T, Q, E0,E1,E2) \</span></div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,0,-1,-2&gt; E0 ## E0; }; \</span></div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,1,-1,-2&gt; E0 ## E1; }; \</span></div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,2,-1,-2&gt; E0 ## E2; }; \</span></div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,0,-1,-2&gt; E1 ## E0; }; \</span></div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,1,-1,-2&gt; E1 ## E1; }; \</span></div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,2,-1,-2&gt; E1 ## E2; }; \</span></div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,0,-1,-2&gt; E2 ## E0; }; \</span></div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,1,-1,-2&gt; E2 ## E1; }; \</span></div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,2,-1,-2&gt; E2 ## E2; };</span></div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE3_3_MEMBERS(T, Q ,E0,E1,E2) \</span></div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,0,-1&gt; E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,1,-1&gt; E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,2,-1&gt; E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,0,-1&gt; E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,1,-1&gt; E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,2,-1&gt; E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,0,-1&gt; E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,1,-1&gt; E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,2,-1&gt; E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,0,-1&gt; E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,1,-1&gt; E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,2,-1&gt; E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,0,-1&gt; E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,1,-1&gt; E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,2,-1&gt; E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,0,-1&gt; E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,1,-1&gt; E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,2,-1&gt; E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,0,-1&gt; E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,1,-1&gt; E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,2,-1&gt; E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,0,-1&gt; E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,1,-1&gt; E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,2,-1&gt; E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,0,-1&gt; E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,1,-1&gt; E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,2,-1&gt; E2 ## E2 ## E2; };</span></div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE3_4_MEMBERS(T, Q, E0,E1,E2) \</span></div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,0,0&gt; E0 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,0,1&gt; E0 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,0,2&gt; E0 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,1,0&gt; E0 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,1,1&gt; E0 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,1,2&gt; E0 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,2,0&gt; E0 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,2,1&gt; E0 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,0,2,2&gt; E0 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,0,0&gt; E0 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,0,1&gt; E0 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,0,2&gt; E0 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,1,0&gt; E0 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,1,1&gt; E0 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,1,2&gt; E0 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,2,0&gt; E0 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,2,1&gt; E0 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,1,2,2&gt; E0 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,0,0&gt; E0 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,0,1&gt; E0 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,0,2&gt; E0 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,1,0&gt; E0 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,1,1&gt; E0 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,1,2&gt; E0 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,2,0&gt; E0 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,2,1&gt; E0 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 0,2,2,2&gt; E0 ## E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,0,0&gt; E1 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,0,1&gt; E1 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,0,2&gt; E1 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,1,0&gt; E1 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,1,1&gt; E1 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,1,2&gt; E1 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,2,0&gt; E1 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,2,1&gt; E1 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,0,2,2&gt; E1 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,0,0&gt; E1 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,0,1&gt; E1 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,0,2&gt; E1 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,1,0&gt; E1 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,1,1&gt; E1 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,1,2&gt; E1 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,2,0&gt; E1 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,2,1&gt; E1 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,1,2,2&gt; E1 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,0,0&gt; E1 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,0,1&gt; E1 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,0,2&gt; E1 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,1,0&gt; E1 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,1,1&gt; E1 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,1,2&gt; E1 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,2,0&gt; E1 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,2,1&gt; E1 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 1,2,2,2&gt; E1 ## E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,0,0&gt; E2 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,0,1&gt; E2 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,0,2&gt; E2 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,1,0&gt; E2 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,1,1&gt; E2 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,1,2&gt; E2 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,2,0&gt; E2 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,2,1&gt; E2 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,0,2,2&gt; E2 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,0,0&gt; E2 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,0,1&gt; E2 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,0,2&gt; E2 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,1,0&gt; E2 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,1,1&gt; E2 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,1,2&gt; E2 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,2,0&gt; E2 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,2,1&gt; E2 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,1,2,2&gt; E2 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,0,0&gt; E2 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,0,1&gt; E2 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,0,2&gt; E2 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,1,0&gt; E2 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,1,1&gt; E2 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,1,2&gt; E2 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,2,0&gt; E2 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,2,1&gt; E2 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4,T, Q, 2,2,2,2&gt; E2 ## E2 ## E2 ## E2; };</span></div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE4_2_MEMBERS(T, Q, E0,E1,E2,E3) \</span></div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,0,-1,-2&gt; E0 ## E0; }; \</span></div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,1,-1,-2&gt; E0 ## E1; }; \</span></div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,2,-1,-2&gt; E0 ## E2; }; \</span></div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 0,3,-1,-2&gt; E0 ## E3; }; \</span></div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,0,-1,-2&gt; E1 ## E0; }; \</span></div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,1,-1,-2&gt; E1 ## E1; }; \</span></div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,2,-1,-2&gt; E1 ## E2; }; \</span></div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 1,3,-1,-2&gt; E1 ## E3; }; \</span></div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,0,-1,-2&gt; E2 ## E0; }; \</span></div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,1,-1,-2&gt; E2 ## E1; }; \</span></div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,2,-1,-2&gt; E2 ## E2; }; \</span></div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 2,3,-1,-2&gt; E2 ## E3; }; \</span></div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 3,0,-1,-2&gt; E3 ## E0; }; \</span></div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 3,1,-1,-2&gt; E3 ## E1; }; \</span></div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 3,2,-1,-2&gt; E3 ## E2; }; \</span></div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;2,T, Q, 3,3,-1,-2&gt; E3 ## E3; };</span></div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE4_3_MEMBERS(T, Q, E0,E1,E2,E3) \</span></div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,0,-1&gt; E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,1,-1&gt; E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,2,-1&gt; E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,0,3,-1&gt; E0 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,0,-1&gt; E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,1,-1&gt; E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,2,-1&gt; E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,1,3,-1&gt; E0 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,0,-1&gt; E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,1,-1&gt; E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,2,-1&gt; E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,2,3,-1&gt; E0 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,3,0,-1&gt; E0 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,3,1,-1&gt; E0 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,3,2,-1&gt; E0 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 0,3,3,-1&gt; E0 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,0,-1&gt; E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,1,-1&gt; E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,2,-1&gt; E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,0,3,-1&gt; E1 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,0,-1&gt; E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,1,-1&gt; E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,2,-1&gt; E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,1,3,-1&gt; E1 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,0,-1&gt; E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,1,-1&gt; E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,2,-1&gt; E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,2,3,-1&gt; E1 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,3,0,-1&gt; E1 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,3,1,-1&gt; E1 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,3,2,-1&gt; E1 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 1,3,3,-1&gt; E1 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,0,-1&gt; E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,1,-1&gt; E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,2,-1&gt; E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,0,3,-1&gt; E2 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,0,-1&gt; E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,1,-1&gt; E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,2,-1&gt; E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,1,3,-1&gt; E2 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,0,-1&gt; E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,1,-1&gt; E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,2,-1&gt; E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,2,3,-1&gt; E2 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,3,0,-1&gt; E2 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,3,1,-1&gt; E2 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,3,2,-1&gt; E2 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 2,3,3,-1&gt; E2 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,0,0,-1&gt; E3 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,0,1,-1&gt; E3 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,0,2,-1&gt; E3 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,0,3,-1&gt; E3 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,1,0,-1&gt; E3 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,1,1,-1&gt; E3 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,1,2,-1&gt; E3 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,1,3,-1&gt; E3 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,2,0,-1&gt; E3 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,2,1,-1&gt; E3 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,2,2,-1&gt; E3 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,2,3,-1&gt; E3 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,3,0,-1&gt; E3 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,3,1,-1&gt; E3 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,3,2,-1&gt; E3 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;3, T, Q, 3,3,3,-1&gt; E3 ## E3 ## E3; };</span></div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;</div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE4_4_MEMBERS(T, Q, E0,E1,E2,E3) \</span></div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,0,0&gt; E0 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,0,1&gt; E0 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,0,2&gt; E0 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,0,3&gt; E0 ## E0 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,1,0&gt; E0 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,1,1&gt; E0 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,1,2&gt; E0 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,1,3&gt; E0 ## E0 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,2,0&gt; E0 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,2,1&gt; E0 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,2,2&gt; E0 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,2,3&gt; E0 ## E0 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,3,0&gt; E0 ## E0 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,3,1&gt; E0 ## E0 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,3,2&gt; E0 ## E0 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,0,3,3&gt; E0 ## E0 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,0,0&gt; E0 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,0,1&gt; E0 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,0,2&gt; E0 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,0,3&gt; E0 ## E1 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,1,0&gt; E0 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,1,1&gt; E0 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,1,2&gt; E0 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,1,3&gt; E0 ## E1 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,2,0&gt; E0 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,2,1&gt; E0 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,2,2&gt; E0 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,2,3&gt; E0 ## E1 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,3,0&gt; E0 ## E1 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,3,1&gt; E0 ## E1 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,3,2&gt; E0 ## E1 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,1,3,3&gt; E0 ## E1 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,0,0&gt; E0 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,0,1&gt; E0 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,0,2&gt; E0 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,0,3&gt; E0 ## E2 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,1,0&gt; E0 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,1,1&gt; E0 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,1,2&gt; E0 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,1,3&gt; E0 ## E2 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,2,0&gt; E0 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,2,1&gt; E0 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,2,2&gt; E0 ## E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,2,3&gt; E0 ## E2 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,3,0&gt; E0 ## E2 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,3,1&gt; E0 ## E2 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,3,2&gt; E0 ## E2 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,2,3,3&gt; E0 ## E2 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,0,0&gt; E0 ## E3 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,0,1&gt; E0 ## E3 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,0,2&gt; E0 ## E3 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,0,3&gt; E0 ## E3 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,1,0&gt; E0 ## E3 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,1,1&gt; E0 ## E3 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,1,2&gt; E0 ## E3 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,1,3&gt; E0 ## E3 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,2,0&gt; E0 ## E3 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,2,1&gt; E0 ## E3 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,2,2&gt; E0 ## E3 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,2,3&gt; E0 ## E3 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,3,0&gt; E0 ## E3 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,3,1&gt; E0 ## E3 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,3,2&gt; E0 ## E3 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 0,3,3,3&gt; E0 ## E3 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,0,0&gt; E1 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,0,1&gt; E1 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,0,2&gt; E1 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,0,3&gt; E1 ## E0 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,1,0&gt; E1 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,1,1&gt; E1 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,1,2&gt; E1 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,1,3&gt; E1 ## E0 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,2,0&gt; E1 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,2,1&gt; E1 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,2,2&gt; E1 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,2,3&gt; E1 ## E0 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,3,0&gt; E1 ## E0 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,3,1&gt; E1 ## E0 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,3,2&gt; E1 ## E0 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,0,3,3&gt; E1 ## E0 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,0,0&gt; E1 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,0,1&gt; E1 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,0,2&gt; E1 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,0,3&gt; E1 ## E1 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,1,0&gt; E1 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,1,1&gt; E1 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,1,2&gt; E1 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,1,3&gt; E1 ## E1 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,2,0&gt; E1 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,2,1&gt; E1 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,2,2&gt; E1 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,2,3&gt; E1 ## E1 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,3,0&gt; E1 ## E1 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,3,1&gt; E1 ## E1 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,3,2&gt; E1 ## E1 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,1,3,3&gt; E1 ## E1 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,0,0&gt; E1 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,0,1&gt; E1 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,0,2&gt; E1 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,0,3&gt; E1 ## E2 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,1,0&gt; E1 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,1,1&gt; E1 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,1,2&gt; E1 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,1,3&gt; E1 ## E2 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,2,0&gt; E1 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,2,1&gt; E1 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,2,2&gt; E1 ## E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,2,3&gt; E1 ## E2 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,3,0&gt; E1 ## E2 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,3,1&gt; E1 ## E2 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,3,2&gt; E1 ## E2 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,2,3,3&gt; E1 ## E2 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,0,0&gt; E1 ## E3 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,0,1&gt; E1 ## E3 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,0,2&gt; E1 ## E3 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,0,3&gt; E1 ## E3 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,1,0&gt; E1 ## E3 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,1,1&gt; E1 ## E3 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,1,2&gt; E1 ## E3 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,1,3&gt; E1 ## E3 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,2,0&gt; E1 ## E3 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,2,1&gt; E1 ## E3 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,2,2&gt; E1 ## E3 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,2,3&gt; E1 ## E3 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,3,0&gt; E1 ## E3 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,3,1&gt; E1 ## E3 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,3,2&gt; E1 ## E3 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 1,3,3,3&gt; E1 ## E3 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,0,0&gt; E2 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,0,1&gt; E2 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,0,2&gt; E2 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,0,3&gt; E2 ## E0 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,1,0&gt; E2 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,1,1&gt; E2 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,1,2&gt; E2 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,1,3&gt; E2 ## E0 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,2,0&gt; E2 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,2,1&gt; E2 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,2,2&gt; E2 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,2,3&gt; E2 ## E0 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,3,0&gt; E2 ## E0 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,3,1&gt; E2 ## E0 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,3,2&gt; E2 ## E0 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,0,3,3&gt; E2 ## E0 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,0,0&gt; E2 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,0,1&gt; E2 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,0,2&gt; E2 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,0,3&gt; E2 ## E1 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,1,0&gt; E2 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,1,1&gt; E2 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,1,2&gt; E2 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,1,3&gt; E2 ## E1 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,2,0&gt; E2 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,2,1&gt; E2 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,2,2&gt; E2 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,2,3&gt; E2 ## E1 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,3,0&gt; E2 ## E1 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,3,1&gt; E2 ## E1 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,3,2&gt; E2 ## E1 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,1,3,3&gt; E2 ## E1 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,0,0&gt; E2 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,0,1&gt; E2 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,0,2&gt; E2 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,0,3&gt; E2 ## E2 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,1,0&gt; E2 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,1,1&gt; E2 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,1,2&gt; E2 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,1,3&gt; E2 ## E2 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,2,0&gt; E2 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,2,1&gt; E2 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,2,2&gt; E2 ## E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,2,3&gt; E2 ## E2 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,3,0&gt; E2 ## E2 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,3,1&gt; E2 ## E2 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,3,2&gt; E2 ## E2 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,2,3,3&gt; E2 ## E2 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,0,0&gt; E2 ## E3 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,0,1&gt; E2 ## E3 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,0,2&gt; E2 ## E3 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,0,3&gt; E2 ## E3 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,1,0&gt; E2 ## E3 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,1,1&gt; E2 ## E3 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,1,2&gt; E2 ## E3 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,1,3&gt; E2 ## E3 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,2,0&gt; E2 ## E3 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,2,1&gt; E2 ## E3 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,2,2&gt; E2 ## E3 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,2,3&gt; E2 ## E3 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,3,0&gt; E2 ## E3 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,3,1&gt; E2 ## E3 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,3,2&gt; E2 ## E3 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 2,3,3,3&gt; E2 ## E3 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,0,0&gt; E3 ## E0 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,0,1&gt; E3 ## E0 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,0,2&gt; E3 ## E0 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,0,3&gt; E3 ## E0 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,1,0&gt; E3 ## E0 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,1,1&gt; E3 ## E0 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,1,2&gt; E3 ## E0 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,1,3&gt; E3 ## E0 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,2,0&gt; E3 ## E0 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,2,1&gt; E3 ## E0 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,2,2&gt; E3 ## E0 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,2,3&gt; E3 ## E0 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,3,0&gt; E3 ## E0 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,3,1&gt; E3 ## E0 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,3,2&gt; E3 ## E0 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,0,3,3&gt; E3 ## E0 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,0,0&gt; E3 ## E1 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,0,1&gt; E3 ## E1 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,0,2&gt; E3 ## E1 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,0,3&gt; E3 ## E1 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,1,0&gt; E3 ## E1 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,1,1&gt; E3 ## E1 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,1,2&gt; E3 ## E1 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,1,3&gt; E3 ## E1 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,2,0&gt; E3 ## E1 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,2,1&gt; E3 ## E1 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,2,2&gt; E3 ## E1 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,2,3&gt; E3 ## E1 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,3,0&gt; E3 ## E1 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,3,1&gt; E3 ## E1 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,3,2&gt; E3 ## E1 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,1,3,3&gt; E3 ## E1 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,0,0&gt; E3 ## E2 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,0,1&gt; E3 ## E2 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,0,2&gt; E3 ## E2 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,0,3&gt; E3 ## E2 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,1,0&gt; E3 ## E2 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,1,1&gt; E3 ## E2 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,1,2&gt; E3 ## E2 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,1,3&gt; E3 ## E2 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,2,0&gt; E3 ## E2 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,2,1&gt; E3 ## E2 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,2,2&gt; E3 ## E2 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,2,3&gt; E3 ## E2 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,3,0&gt; E3 ## E2 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,3,1&gt; E3 ## E2 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,3,2&gt; E3 ## E2 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,2,3,3&gt; E3 ## E2 ## E3 ## E3; }; \</span></div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,0,0&gt; E3 ## E3 ## E0 ## E0; }; \</span></div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,0,1&gt; E3 ## E3 ## E0 ## E1; }; \</span></div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,0,2&gt; E3 ## E3 ## E0 ## E2; }; \</span></div>
<div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,0,3&gt; E3 ## E3 ## E0 ## E3; }; \</span></div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,1,0&gt; E3 ## E3 ## E1 ## E0; }; \</span></div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,1,1&gt; E3 ## E3 ## E1 ## E1; }; \</span></div>
<div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,1,2&gt; E3 ## E3 ## E1 ## E2; }; \</span></div>
<div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,1,3&gt; E3 ## E3 ## E1 ## E3; }; \</span></div>
<div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,2,0&gt; E3 ## E3 ## E2 ## E0; }; \</span></div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,2,1&gt; E3 ## E3 ## E2 ## E1; }; \</span></div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,2,2&gt; E3 ## E3 ## E2 ## E2; }; \</span></div>
<div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,2,3&gt; E3 ## E3 ## E2 ## E3; }; \</span></div>
<div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,3,0&gt; E3 ## E3 ## E3 ## E0; }; \</span></div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,3,1&gt; E3 ## E3 ## E3 ## E1; }; \</span></div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,3,2&gt; E3 ## E3 ## E3 ## E2; }; \</span></div>
<div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;<span class="preprocessor">        struct { detail::_swizzle&lt;4, T, Q, 3,3,3,3&gt; E3 ## E3 ## E3 ## E3; };</span></div>
<div class="ttc" id="a00290_html_ga4b7956eb6e2fbedfc7cf2e46e85c5139"><div class="ttname"><a href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">glm::e</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType e()</div><div class="ttdoc">Return e constant. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
