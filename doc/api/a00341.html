<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_matrix_transform_2d</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_matrix_transform_2d<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00110.html" title="GLM_GTX_matrix_transform_2d ">glm/gtx/matrix_transform_2d.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad5c84a4932a758f385a87098ce1b1660"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad5c84a4932a758f385a87098ce1b1660"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00341.html#gad5c84a4932a758f385a87098ce1b1660">rotate</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T angle)</td></tr>
<tr class="memdesc:gad5c84a4932a758f385a87098ce1b1660"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a rotation 3 * 3 matrix created from an angle.  <a href="a00341.html#gad5c84a4932a758f385a87098ce1b1660">More...</a><br /></td></tr>
<tr class="separator:gad5c84a4932a758f385a87098ce1b1660"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadb47d2ad2bd984b213e8ff7d9cd8154e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gadb47d2ad2bd984b213e8ff7d9cd8154e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00341.html#gadb47d2ad2bd984b213e8ff7d9cd8154e">scale</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gadb47d2ad2bd984b213e8ff7d9cd8154e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a scale 3 * 3 matrix created from a vector of 2 components.  <a href="a00341.html#gadb47d2ad2bd984b213e8ff7d9cd8154e">More...</a><br /></td></tr>
<tr class="separator:gadb47d2ad2bd984b213e8ff7d9cd8154e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a118ece5db1e2022112b954846012af"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2a118ece5db1e2022112b954846012af"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00341.html#ga2a118ece5db1e2022112b954846012af">shearX</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T y)</td></tr>
<tr class="memdesc:ga2a118ece5db1e2022112b954846012af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds an horizontal (parallel to the x axis) shear 3 * 3 matrix.  <a href="a00341.html#ga2a118ece5db1e2022112b954846012af">More...</a><br /></td></tr>
<tr class="separator:ga2a118ece5db1e2022112b954846012af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga717f1833369c1ac4a40e4ac015af885e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga717f1833369c1ac4a40e4ac015af885e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00341.html#ga717f1833369c1ac4a40e4ac015af885e">shearY</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T x)</td></tr>
<tr class="memdesc:ga717f1833369c1ac4a40e4ac015af885e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a vertical (parallel to the y axis) shear 3 * 3 matrix.  <a href="a00341.html#ga717f1833369c1ac4a40e4ac015af885e">More...</a><br /></td></tr>
<tr class="separator:ga717f1833369c1ac4a40e4ac015af885e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4573ae47c80938aa9053ef6a33755ab"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf4573ae47c80938aa9053ef6a33755ab"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00341.html#gaf4573ae47c80938aa9053ef6a33755ab">translate</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaf4573ae47c80938aa9053ef6a33755ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a translation 3 * 3 matrix created from a vector of 2 components.  <a href="a00341.html#gaf4573ae47c80938aa9053ef6a33755ab">More...</a><br /></td></tr>
<tr class="separator:gaf4573ae47c80938aa9053ef6a33755ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00110.html" title="GLM_GTX_matrix_transform_2d ">glm/gtx/matrix_transform_2d.hpp</a>&gt; to use the features of this extension. </p>
<p>Defines functions that generate common 2d transformation matrices. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gad5c84a4932a758f385a87098ce1b1660"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a rotation 3 * 3 matrix created from an angle. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this translation matrix. </td></tr>
    <tr><td class="paramname">angle</td><td>Rotation angle expressed in radians. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gadb47d2ad2bd984b213e8ff7d9cd8154e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; glm::scale </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a scale 3 * 3 matrix created from a vector of 2 components. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this translation matrix. </td></tr>
    <tr><td class="paramname">v</td><td>Coordinates of a scale vector. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga2a118ece5db1e2022112b954846012af"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; glm::shearX </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds an horizontal (parallel to the x axis) shear 3 * 3 matrix. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this translation matrix. </td></tr>
    <tr><td class="paramname">y</td><td>Shear factor. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga717f1833369c1ac4a40e4ac015af885e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; glm::shearY </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a vertical (parallel to the y axis) shear 3 * 3 matrix. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this translation matrix. </td></tr>
    <tr><td class="paramname">x</td><td>Shear factor. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaf4573ae47c80938aa9053ef6a33755ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER mat&lt;3, 3, T, Q&gt; glm::translate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a translation 3 * 3 matrix created from a vector of 2 components. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">m</td><td>Input matrix multiplied by this translation matrix. </td></tr>
    <tr><td class="paramname">v</td><td>Coordinates of a translation vector. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
