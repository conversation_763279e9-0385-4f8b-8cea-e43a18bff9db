<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">matrix.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00280.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00057_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad7928795124768e058f99dce270f5c8d"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad7928795124768e058f99dce270f5c8d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gad7928795124768e058f99dce270f5c8d">determinant</a> (mat&lt; C, R, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gad7928795124768e058f99dce270f5c8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the determinant of a squared matrix.  <a href="a00371.html#gad7928795124768e058f99dce270f5c8d">More...</a><br /></td></tr>
<tr class="separator:gad7928795124768e058f99dce270f5c8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed509fe8129b01e4f20a6d0de5690091"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaed509fe8129b01e4f20a6d0de5690091"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gaed509fe8129b01e4f20a6d0de5690091">inverse</a> (mat&lt; C, R, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gaed509fe8129b01e4f20a6d0de5690091"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the inverse of a squared matrix.  <a href="a00371.html#gaed509fe8129b01e4f20a6d0de5690091">More...</a><br /></td></tr>
<tr class="separator:gaed509fe8129b01e4f20a6d0de5690091"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf14569404c779fedca98d0b9b8e58c1f"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf14569404c779fedca98d0b9b8e58c1f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gaf14569404c779fedca98d0b9b8e58c1f">matrixCompMult</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gaf14569404c779fedca98d0b9b8e58c1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiply matrix x by matrix y component-wise, i.e., result[i][j] is the scalar product of x[i][j] and y[i][j].  <a href="a00371.html#gaf14569404c779fedca98d0b9b8e58c1f">More...</a><br /></td></tr>
<tr class="separator:gaf14569404c779fedca98d0b9b8e58c1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL detail::outerProduct_trait&lt; C, R, T, Q &gt;::type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gac29fb7bae75a8e4c1b74cbbf85520e50">outerProduct</a> (vec&lt; C, T, Q &gt; const &amp;c, vec&lt; R, T, Q &gt; const &amp;r)</td></tr>
<tr class="memdesc:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="mdescLeft">&#160;</td><td class="mdescRight">Treats the first parameter c as a column vector and the second parameter r as a row vector and does a linear algebraic matrix multiply c * r.  <a href="a00371.html#gac29fb7bae75a8e4c1b74cbbf85520e50">More...</a><br /></td></tr>
<tr class="separator:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;::transpose_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gae679d841da8ce9dbcc6c2d454f15bc35">transpose</a> (mat&lt; C, R, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the transposed matrix of x.  <a href="a00371.html#gae679d841da8ce9dbcc6c2d454f15bc35">More...</a><br /></td></tr>
<tr class="separator:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00280.html">Core features</a> </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

<p>Definition in file <a class="el" href="a00057_source.html">matrix.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
