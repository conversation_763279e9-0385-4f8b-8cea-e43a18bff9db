<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_noise</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_noise<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00112.html" title="GLM_GTC_noise ">glm/gtc/noise.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga1e043ce3b51510e9bc4469227cefc38a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1e043ce3b51510e9bc4469227cefc38a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00297.html#ga1e043ce3b51510e9bc4469227cefc38a">perlin</a> (vec&lt; L, T, Q &gt; const &amp;p)</td></tr>
<tr class="memdesc:ga1e043ce3b51510e9bc4469227cefc38a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Classic perlin noise.  <a href="a00297.html#ga1e043ce3b51510e9bc4469227cefc38a">More...</a><br /></td></tr>
<tr class="separator:ga1e043ce3b51510e9bc4469227cefc38a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac270edc54c5fc52f5985a45f940bb103"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac270edc54c5fc52f5985a45f940bb103"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00297.html#gac270edc54c5fc52f5985a45f940bb103">perlin</a> (vec&lt; L, T, Q &gt; const &amp;p, vec&lt; L, T, Q &gt; const &amp;rep)</td></tr>
<tr class="memdesc:gac270edc54c5fc52f5985a45f940bb103"><td class="mdescLeft">&#160;</td><td class="mdescRight">Periodic perlin noise.  <a href="a00297.html#gac270edc54c5fc52f5985a45f940bb103">More...</a><br /></td></tr>
<tr class="separator:gac270edc54c5fc52f5985a45f940bb103"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8122468c69015ff397349a7dcc638b27"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8122468c69015ff397349a7dcc638b27"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00297.html#ga8122468c69015ff397349a7dcc638b27">simplex</a> (vec&lt; L, T, Q &gt; const &amp;p)</td></tr>
<tr class="memdesc:ga8122468c69015ff397349a7dcc638b27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Simplex noise.  <a href="a00297.html#ga8122468c69015ff397349a7dcc638b27">More...</a><br /></td></tr>
<tr class="separator:ga8122468c69015ff397349a7dcc638b27"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00112.html" title="GLM_GTC_noise ">glm/gtc/noise.hpp</a>&gt; to use the features of this extension. </p>
<p>Defines 2D, 3D and 4D procedural noise functions Based on the work of Stefan Gustavson and Ashima Arts on "webgl-noise": <a href="https://github.com/ashima/webgl-noise">https://github.com/ashima/webgl-noise</a> Following Stefan Gustavson's paper "Simplex noise demystified": <a href="http://www.itn.liu.se/~stegu/simplexnoise/simplexnoise.pdf">http://www.itn.liu.se/~stegu/simplexnoise/simplexnoise.pdf</a> </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga1e043ce3b51510e9bc4469227cefc38a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::perlin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Classic perlin noise. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00297.html" title="Include <glm/gtc/noise.hpp> to use the features of this extension. ">GLM_GTC_noise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac270edc54c5fc52f5985a45f940bb103"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::perlin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rep</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Periodic perlin noise. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00297.html" title="Include <glm/gtc/noise.hpp> to use the features of this extension. ">GLM_GTC_noise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8122468c69015ff397349a7dcc638b27"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::simplex </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Simplex noise. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00297.html" title="Include <glm/gtc/noise.hpp> to use the features of this extension. ">GLM_GTC_noise</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
