<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: integer.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">integer.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00043.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00015.html">common.hpp</a>&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00225.html">vector_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;{</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        GLM_FUNC_DECL vec&lt;L, uint, Q&gt; <a class="code" href="a00370.html#gaedcec48743632dff6786bcc492074b1b">uaddCarry</a>(</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                vec&lt;L, uint, Q&gt; &amp; carry);</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_FUNC_DECL vec&lt;L, uint, Q&gt; <a class="code" href="a00370.html#gae3316ba1229ad9b9f09480833321b053">usubBorrow</a>(</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                vec&lt;L, uint, Q&gt; &amp; borrow);</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">void</span> <a class="code" href="a00370.html#ga732e2fb56db57ea541c7e5c92b7121be">umulExtended</a>(</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                vec&lt;L, uint, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                vec&lt;L, uint, Q&gt; &amp; msb,</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                vec&lt;L, uint, Q&gt; &amp; lsb);</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">void</span> <a class="code" href="a00370.html#gac0c510a70e852f57594a9141848642e3">imulExtended</a>(</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                vec&lt;L, int, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                vec&lt;L, int, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                vec&lt;L, int, Q&gt; &amp; msb,</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                vec&lt;L, int, Q&gt; &amp; lsb);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00370.html#ga346b25ab11e793e91a4a69c8aa6819f2">bitfieldExtract</a>(</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Value,</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                <span class="keywordtype">int</span> Offset,</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                <span class="keywordtype">int</span> Bits);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00370.html#ga2e82992340d421fadb61a473df699b20">bitfieldInsert</a>(</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Base,</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Insert,</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                <span class="keywordtype">int</span> Offset,</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                <span class="keywordtype">int</span> Bits);</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00370.html#ga750a1d92464489b7711dee67aa3441b6">bitfieldReverse</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">int</span> <a class="code" href="a00370.html#gaac7b15e40bdea8d9aa4c4cb34049f7b5">bitCount</a>(genType v);</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        GLM_FUNC_DECL vec&lt;L, int, Q&gt; <a class="code" href="a00370.html#gaac7b15e40bdea8d9aa4c4cb34049f7b5">bitCount</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">int</span> <a class="code" href="a00370.html#ga4454c0331d6369888c28ab677f4810c7">findLSB</a>(genIUType x);</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        GLM_FUNC_DECL vec&lt;L, int, Q&gt; <a class="code" href="a00370.html#ga4454c0331d6369888c28ab677f4810c7">findLSB</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">int</span> <a class="code" href="a00370.html#ga39ac4d52028bb6ab08db5ad6562c2872">findMSB</a>(genIUType x);</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        GLM_FUNC_DECL vec&lt;L, int, Q&gt; <a class="code" href="a00370.html#ga39ac4d52028bb6ab08db5ad6562c2872">findMSB</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="preprocessor">#include &quot;detail/func_integer.inl&quot;</span></div>
<div class="ttc" id="a00015_html"><div class="ttname"><a href="a00015.html">common.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00370_html_ga39ac4d52028bb6ab08db5ad6562c2872"><div class="ttname"><a href="a00370.html#ga39ac4d52028bb6ab08db5ad6562c2872">glm::findMSB</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, int, Q &gt; findMSB(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the bit number of the most significant bit in the binary representation of value...</div></div>
<div class="ttc" id="a00370_html_ga732e2fb56db57ea541c7e5c92b7121be"><div class="ttname"><a href="a00370.html#ga732e2fb56db57ea541c7e5c92b7121be">glm::umulExtended</a></div><div class="ttdeci">GLM_FUNC_DECL void umulExtended(vec&lt; L, uint, Q &gt; const &amp;x, vec&lt; L, uint, Q &gt; const &amp;y, vec&lt; L, uint, Q &gt; &amp;msb, vec&lt; L, uint, Q &gt; &amp;lsb)</div><div class="ttdoc">Multiplies 32-bit integers x and y, producing a 64-bit result. </div></div>
<div class="ttc" id="a00370_html_gac0c510a70e852f57594a9141848642e3"><div class="ttname"><a href="a00370.html#gac0c510a70e852f57594a9141848642e3">glm::imulExtended</a></div><div class="ttdeci">GLM_FUNC_DECL void imulExtended(vec&lt; L, int, Q &gt; const &amp;x, vec&lt; L, int, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; &amp;msb, vec&lt; L, int, Q &gt; &amp;lsb)</div><div class="ttdoc">Multiplies 32-bit integers x and y, producing a 64-bit result. </div></div>
<div class="ttc" id="a00370_html_gaac7b15e40bdea8d9aa4c4cb34049f7b5"><div class="ttname"><a href="a00370.html#gaac7b15e40bdea8d9aa4c4cb34049f7b5">glm::bitCount</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, int, Q &gt; bitCount(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the number of bits set to 1 in the binary representation of value. </div></div>
<div class="ttc" id="a00370_html_gaedcec48743632dff6786bcc492074b1b"><div class="ttname"><a href="a00370.html#gaedcec48743632dff6786bcc492074b1b">glm::uaddCarry</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, uint, Q &gt; uaddCarry(vec&lt; L, uint, Q &gt; const &amp;x, vec&lt; L, uint, Q &gt; const &amp;y, vec&lt; L, uint, Q &gt; &amp;carry)</div><div class="ttdoc">Adds 32-bit unsigned integer x and y, returning the sum modulo pow(2, 32). </div></div>
<div class="ttc" id="a00370_html_ga346b25ab11e793e91a4a69c8aa6819f2"><div class="ttname"><a href="a00370.html#ga346b25ab11e793e91a4a69c8aa6819f2">glm::bitfieldExtract</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldExtract(vec&lt; L, T, Q &gt; const &amp;Value, int Offset, int Bits)</div><div class="ttdoc">Extracts bits [offset, offset + bits - 1] from value, returning them in the least significant bits of...</div></div>
<div class="ttc" id="a00370_html_ga2e82992340d421fadb61a473df699b20"><div class="ttname"><a href="a00370.html#ga2e82992340d421fadb61a473df699b20">glm::bitfieldInsert</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldInsert(vec&lt; L, T, Q &gt; const &amp;Base, vec&lt; L, T, Q &gt; const &amp;Insert, int Offset, int Bits)</div><div class="ttdoc">Returns the insertion the bits least-significant bits of insert into base. </div></div>
<div class="ttc" id="a00225_html"><div class="ttname"><a href="a00225.html">vector_relational.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00370_html_ga750a1d92464489b7711dee67aa3441b6"><div class="ttname"><a href="a00370.html#ga750a1d92464489b7711dee67aa3441b6">glm::bitfieldReverse</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldReverse(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the reversal of the bits of value. </div></div>
<div class="ttc" id="a00370_html_gae3316ba1229ad9b9f09480833321b053"><div class="ttname"><a href="a00370.html#gae3316ba1229ad9b9f09480833321b053">glm::usubBorrow</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, uint, Q &gt; usubBorrow(vec&lt; L, uint, Q &gt; const &amp;x, vec&lt; L, uint, Q &gt; const &amp;y, vec&lt; L, uint, Q &gt; &amp;borrow)</div><div class="ttdoc">Subtracts the 32-bit unsigned integer y from x, returning the difference if non-negative, or pow(2, 32) plus the difference otherwise. </div></div>
<div class="ttc" id="a00370_html_ga4454c0331d6369888c28ab677f4810c7"><div class="ttname"><a href="a00370.html#ga4454c0331d6369888c28ab677f4810c7">glm::findLSB</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, int, Q &gt; findLSB(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the bit number of the least significant bit set to 1 in the binary representation of value...</div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
