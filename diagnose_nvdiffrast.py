#!/usr/bin/env python3
"""
诊断nvdiffrast问题
"""

import sys
import os
import subprocess
import platform

def check_system_info():
    """检查系统信息"""
    print("🖥️ 系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  架构: {platform.machine()}")
    print(f"  Python版本: {sys.version}")
    print()

def check_visual_cpp():
    """检查Visual C++安装"""
    print("🔍 检查Visual C++安装:")
    
    # 检查注册表中的Visual C++安装
    try:
        import winreg
        
        # 检查不同版本的Visual C++
        vc_versions = [
            ("2015-2022", r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\X64"),
            ("2019", r"SOFTWARE\Microsoft\VisualStudio\16.0\VC\Runtimes\X64"),
            ("2017", r"SOFTWARE\Microsoft\VisualStudio\15.0\VC\Runtimes\X64"),
        ]
        
        found_any = False
        for version, key_path in vc_versions:
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                version_value = winreg.QueryValueEx(key, "Version")[0]
                print(f"  ✅ Visual C++ {version}: {version_value}")
                found_any = True
                winreg.CloseKey(key)
            except FileNotFoundError:
                print(f"  ❌ Visual C++ {version}: 未安装")
            except Exception as e:
                print(f"  ⚠️ Visual C++ {version}: 检查失败 ({e})")
        
        if not found_any:
            print("  ❌ 未找到任何Visual C++安装")
        
    except ImportError:
        print("  ⚠️ 无法检查注册表（winreg不可用）")
    
    print()

def check_cuda_and_gpu():
    """检查CUDA和GPU"""
    print("🎮 检查CUDA和GPU:")
    
    # 检查NVIDIA驱动
    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if "Driver Version" in line:
                    print(f"  ✅ NVIDIA驱动: {line.strip()}")
                    break
        else:
            print("  ❌ nvidia-smi命令失败")
    except FileNotFoundError:
        print("  ❌ nvidia-smi未找到（可能没有NVIDIA GPU）")
    
    # 检查CUDA
    try:
        result = subprocess.run(["nvcc", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if "release" in line:
                    print(f"  ✅ CUDA编译器: {line.strip()}")
                    break
        else:
            print("  ❌ nvcc命令失败")
    except FileNotFoundError:
        print("  ❌ nvcc未找到")
    
    # 检查PyTorch CUDA
    try:
        import torch
        print(f"  ✅ PyTorch CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"  ✅ CUDA设备数量: {torch.cuda.device_count()}")
            print(f"  ✅ 当前CUDA设备: {torch.cuda.current_device()}")
            print(f"  ✅ GPU名称: {torch.cuda.get_device_name()}")
    except ImportError:
        print("  ❌ PyTorch未安装")
    
    print()

def check_opengl():
    """检查OpenGL支持"""
    print("🎨 检查OpenGL支持:")
    
    try:
        import OpenGL.GL as gl
        print("  ✅ PyOpenGL已安装")
        
        # 尝试获取OpenGL信息（需要上下文）
        try:
            import pygame
            pygame.init()
            pygame.display.set_mode((1, 1), pygame.OPENGL | pygame.HIDDEN)
            
            vendor = gl.glGetString(gl.GL_VENDOR).decode()
            renderer = gl.glGetString(gl.GL_RENDERER).decode()
            version = gl.glGetString(gl.GL_VERSION).decode()
            
            print(f"  ✅ OpenGL厂商: {vendor}")
            print(f"  ✅ OpenGL渲染器: {renderer}")
            print(f"  ✅ OpenGL版本: {version}")
            
            pygame.quit()
            
        except ImportError:
            print("  ⚠️ pygame未安装，无法获取OpenGL详细信息")
        except Exception as e:
            print(f"  ⚠️ 获取OpenGL信息失败: {e}")
            
    except ImportError:
        print("  ❌ PyOpenGL未安装")
    
    print()

def check_nvdiffrast():
    """检查nvdiffrast"""
    print("🔧 检查nvdiffrast:")
    
    try:
        import nvdiffrast
        print(f"  ✅ nvdiffrast已安装，版本: {nvdiffrast.__version__}")
        
        # 尝试创建不同类型的上下文
        print("  🧪 测试nvdiffrast上下文:")
        
        # 测试CUDA上下文
        try:
            import nvdiffrast.torch as dr
            glctx = dr.RasterizeCudaContext()
            print("  ✅ CUDA光栅化上下文创建成功")
            del glctx
        except Exception as e:
            print(f"  ❌ CUDA光栅化上下文失败: {e}")
        
        # 测试OpenGL上下文
        try:
            import nvdiffrast.torch as dr
            glctx = dr.RasterizeGLContext()
            print("  ✅ OpenGL光栅化上下文创建成功")
            del glctx
        except Exception as e:
            print(f"  ❌ OpenGL光栅化上下文失败: {e}")
            print(f"     错误详情: {str(e)}")
            
            # 分析具体错误
            if "Could not locate a supported Microsoft Visual C++ installation" in str(e):
                print("     🔍 这是Visual C++问题")
            elif "OpenGL" in str(e):
                print("     🔍 这是OpenGL问题")
            elif "CUDA" in str(e):
                print("     🔍 这是CUDA问题")
        
    except ImportError:
        print("  ❌ nvdiffrast未安装")
    
    print()

def check_environment_variables():
    """检查环境变量"""
    print("🌍 检查相关环境变量:")
    
    env_vars = [
        "CUDA_HOME",
        "CUDA_PATH", 
        "PATH",
        "NVDIFFRAST_FORCE_CPU",
        "CUDA_VISIBLE_DEVICES"
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            if var == "PATH":
                # PATH太长，只显示CUDA相关部分
                cuda_paths = [p for p in value.split(';') if 'cuda' in p.lower()]
                if cuda_paths:
                    print(f"  ✅ {var} (CUDA相关): {'; '.join(cuda_paths[:3])}")
                    if len(cuda_paths) > 3:
                        print(f"      ... 还有 {len(cuda_paths)-3} 个CUDA路径")
                else:
                    print(f"  ⚠️ {var}: 未找到CUDA相关路径")
            else:
                print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: 未设置")
    
    print()

def suggest_solutions():
    """建议解决方案"""
    print("💡 可能的解决方案:")
    print("1. 🔄 重启计算机（确保Visual C++完全生效）")
    print("2. 🎮 更新NVIDIA显卡驱动到最新版本")
    print("3. 🔧 重新安装nvdiffrast:")
    print("   pip uninstall nvdiffrast")
    print("   pip install git+https://github.com/NVlabs/nvdiffrast/")
    print("4. 🖥️ 尝试使用CUDA光栅化而不是OpenGL:")
    print("   修改代码使用 dr.RasterizeCudaContext() 而不是 dr.RasterizeGLContext()")
    print("5. 💻 使用CPU模式（较慢但更兼容）:")
    print("   设置环境变量 NVDIFFRAST_FORCE_CPU=1")
    print()

if __name__ == '__main__':
    print("🔍 nvdiffrast问题诊断工具")
    print("=" * 60)
    
    check_system_info()
    check_visual_cpp()
    check_cuda_and_gpu()
    check_opengl()
    check_nvdiffrast()
    check_environment_variables()
    suggest_solutions()
    
    print("🏁 诊断完成")
    print("请根据上述信息确定问题原因并选择合适的解决方案")
