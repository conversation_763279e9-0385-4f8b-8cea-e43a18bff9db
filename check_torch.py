#!/usr/bin/env python3
"""检查 PyTorch 安装状态"""

try:
    import torch
    print(f"✅ PyTorch 已安装，版本: {torch.__version__}")
    print(f"✅ CUDA 可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"✅ CUDA 版本: {torch.version.cuda}")
        print(f"✅ GPU 数量: {torch.cuda.device_count()}")
        print(f"✅ 当前 GPU: {torch.cuda.get_device_name()}")
    else:
        print("❌ CUDA 不可用")
except ImportError:
    print("❌ PyTorch 未安装")
