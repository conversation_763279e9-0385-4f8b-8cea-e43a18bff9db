#!/usr/bin/env python3
"""
将stable_zero123的ckpt文件转换为diffusers格式
"""

import os
import torch
from pathlib import Path
import shutil

def convert_ckpt_to_diffusers():
    """转换ckpt到diffusers格式"""
    print("🔄 转换stable_zero123 ckpt到diffusers格式...")
    
    ckpt_path = Path("C:/Users/<USER>/Downloads/stable_zero123.ckpt")
    output_dir = Path("models/stable_zero123_diffusers")
    
    if not ckpt_path.exists():
        print(f"❌ 找不到ckpt文件: {ckpt_path}")
        return False
    
    print(f"📁 输入文件: {ckpt_path}")
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载checkpoint
        print("📖 加载checkpoint...")
        checkpoint = torch.load(ckpt_path, map_location='cpu')
        
        print(f"📋 Checkpoint包含的键: {list(checkpoint.keys())}")
        
        # 检查是否有state_dict
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
            print(f"📋 State dict包含 {len(state_dict)} 个参数")
        else:
            state_dict = checkpoint
            print(f"📋 直接使用checkpoint作为state dict")
        
        # 分析模型结构
        print("\n🔍 分析模型结构:")
        component_keys = {}
        for key in state_dict.keys():
            if key.startswith('model.'):
                component = key.split('.')[1]
                if component not in component_keys:
                    component_keys[component] = []
                component_keys[component].append(key)
        
        for component, keys in component_keys.items():
            print(f"  📦 {component}: {len(keys)} 个参数")
        
        # 这里需要根据stable_zero123的具体结构来转换
        # 由于这是一个复杂的转换过程，我们先尝试一个简化的方法
        
        # 保存原始checkpoint到新位置
        converted_ckpt_path = output_dir / "pytorch_model.bin"
        torch.save(state_dict, converted_ckpt_path)
        print(f"✅ 保存转换后的模型: {converted_ckpt_path}")
        
        # 创建基本的config.json
        config = {
            "_class_name": "StableZero123Pipeline",
            "_diffusers_version": "0.21.4",
            "_name_or_path": "ashawkey/stable-zero123-diffusers",
            "feature_extractor": ["transformers", "CLIPImageProcessor"],
            "image_encoder": ["transformers", "CLIPVisionModel"],
            "scheduler": ["diffusers", "DDIMScheduler"],
            "unet": ["diffusers", "UNet2DConditionModel"],
            "vae": ["diffusers", "AutoencoderKL"],
            "requires_safety_checker": False
        }
        
        import json
        config_path = output_dir / "model_index.json"
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"✅ 创建配置文件: {config_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def setup_offline_stable_zero123():
    """设置离线stable_zero123环境"""
    print("🔧 设置离线stable_zero123环境...")
    
    # 创建HuggingFace缓存目录结构
    cache_dir = Path.home() / '.cache' / 'huggingface' / 'hub'
    model_dir = cache_dir / 'models--ashawkey--stable-zero123-diffusers'
    
    # 检查是否已经有diffusers格式的模型
    local_diffusers_dir = Path("models/stable_zero123_diffusers")
    
    if local_diffusers_dir.exists():
        print(f"✅ 找到本地diffusers模型: {local_diffusers_dir}")
        
        # 创建符号链接到HuggingFace缓存
        snapshots_dir = model_dir / 'snapshots' / 'main'
        snapshots_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制文件到缓存目录
        for item in local_diffusers_dir.iterdir():
            target = snapshots_dir / item.name
            if item.is_file() and not target.exists():
                shutil.copy2(item, target)
                print(f"📋 复制: {item.name}")
        
        # 创建refs文件
        refs_dir = model_dir / 'refs'
        refs_dir.mkdir(exist_ok=True)
        with open(refs_dir / 'main', 'w') as f:
            f.write('main')
        
        print("✅ HuggingFace缓存设置完成")
        return True
    else:
        print("❌ 未找到本地diffusers模型")
        return False

def create_simple_local_config():
    """创建简单的本地配置，直接使用ckpt文件"""
    print("📝 创建简单的本地配置...")
    
    # 修改guidance/zero123_utils.py以支持直接加载ckpt
    config_content = """
# 简化的image配置，使用本地ckpt文件
name: "image_simple"
tag: ""
seed: 0

# 输入输出
input: data/anya_rgba.png
save_path: anya_simple
outdir: logs

# 训练参数
iters: 500
batch_size: 1
radius: 2
fovy: 49.1
min_ver: -30
max_ver: 30

# 使用stable_zero123
stable_zero123: True
lambda_zero123: 1
lambda_sd: 0

# 网格参数
density_thresh: 1
mesh_format: obj

# 系统参数
gui: False
force_cuda_rast: True

# Gaussian参数
num_pts: 5000
sh_degree: 0
"""
    
    config_path = Path("configs/image_simple.yaml")
    with open(config_path, 'w') as f:
        f.write(config_content.strip())
    
    print(f"✅ 简单配置已创建: {config_path}")
    return config_path

def main():
    """主函数"""
    print("🔄 stable_zero123 ckpt转换工具")
    print("=" * 50)
    
    # 方法1: 尝试转换ckpt到diffusers格式
    print("\n📋 方法1: 转换ckpt到diffusers格式")
    if convert_ckpt_to_diffusers():
        if setup_offline_stable_zero123():
            print("✅ 方法1成功！可以使用diffusers格式")
        else:
            print("⚠️ 方法1部分成功，缓存设置失败")
    else:
        print("❌ 方法1失败")
    
    # 方法2: 创建简单配置
    print("\n📋 方法2: 创建简化配置")
    config_path = create_simple_local_config()
    
    print("\n💡 建议:")
    print("1. 首先尝试运行: python main.py --config configs/image_simple.yaml")
    print("2. 如果失败，可能需要手动修改代码以支持ckpt格式")
    print("3. 或者尝试在线下载diffusers格式的stable_zero123模型")
    
    return True

if __name__ == '__main__':
    main()
