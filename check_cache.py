#!/usr/bin/env python3
"""
检查HuggingFace缓存状态
"""

import os
from pathlib import Path

def check_huggingface_cache():
    """检查HuggingFace缓存目录"""
    cache_dir = Path.home() / '.cache' / 'huggingface' / 'hub'
    model_dir = cache_dir / 'models--ashawkey--zero123-xl-diffusers'
    
    print(f"🔍 检查缓存目录: {cache_dir}")
    print(f"📁 模型目录: {model_dir}")
    
    if not model_dir.exists():
        print("❌ 模型目录不存在")
        return False
    
    snapshots_dir = model_dir / 'snapshots' / 'main'
    if not snapshots_dir.exists():
        print("❌ snapshots/main 目录不存在")
        return False
    
    print(f"📂 快照目录: {snapshots_dir}")
    
    # 检查核心文件
    core_files = [
        'model_index.json',
        'feature_extractor/preprocessor_config.json',
        'image_encoder/config.json',
        'image_encoder/model.safetensors',
        'scheduler/scheduler_config.json',
        'unet/config.json',
        'unet/diffusion_pytorch_model.safetensors',
        'vae/config.json',
        'vae/diffusion_pytorch_model.safetensors'
    ]
    
    print("\n📋 检查核心文件:")
    missing_files = []
    for file_path in core_files:
        full_path = snapshots_dir / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"✅ {file_path} ({size:,} 字节)")
        else:
            print(f"❌ {file_path} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺失 {len(missing_files)} 个文件")
        return False
    else:
        print(f"\n🎉 所有核心文件都存在！")
        
        # 创建refs目录和main文件（HuggingFace Hub需要）
        refs_dir = model_dir / 'refs'
        refs_dir.mkdir(exist_ok=True)
        
        main_ref = refs_dir / 'main'
        if not main_ref.exists():
            # 写入commit hash（使用一个假的hash，因为我们是手动下载的）
            with open(main_ref, 'w') as f:
                f.write('main')
            print("✅ 创建了 refs/main 文件")
        
        return True

if __name__ == '__main__':
    check_huggingface_cache()
